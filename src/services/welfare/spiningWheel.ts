import { API_ADSE } from 'constantsV2/apiConfig';
import request, { ResDataType } from 'servicesV2/request';
import { encryptByType, getXuidTicket } from 'utilsV2/native';
import { XUID_ticketConfig } from 'constantsV2';
import userInfoDetail from 'modulesV2/userInfoDetail';
import uuid from 'utilsV2/uuid';

export enum SpinStatus {
  IDLE = 'idle',
  SPINNING = 'spinning',
  REFRESHING = 'refreshing',
  FINISHED = 'finished',
  ERROR = 'error'
}

export interface SpinningWheelPrize {
  id: number;
  amountText: string;
  icon: string;
  awardType: number;
  cashText?: string;
  fallback?: boolean;
}

export interface Winner {
  name: string;
  award: string;
  avatar?: string;
}

export interface SpinningWheelInfo {
  success: boolean;
  code: number;
  firstFree: boolean;
  title: string;
  costCoins: number;
  buttonText: string;
  prizes: SpinningWheelPrize[];
  winnerList: Winner[];
}

export interface SpinResult {
  success: boolean;
  code: number;
  awardCode: string;
  awardIndex: number;
  retry: boolean;
  toast: string;
}

export interface SpinRequest {
  requestId: string;
  ts: number;
  retry: number;
  ticket: string;
  signature: string;
}

export interface SpinError {
  code: number;
  message: string;
  source: 'api' | 'ad' | 'animation' | 'reward' | 'spin';
  details?: any;
}

/**
 * 查询转盘抽奖信息
 * @returns 转盘配置信息
 */
export const querySpinningWheelInfo = async (): Promise<ResDataType<SpinningWheelInfo> | undefined> => {
  return request<SpinningWheelInfo>({
    ...API_ADSE,
    url: `incentive/ting/welfare/queryRotaryTableInfo/ts-${Date.now()}`,
    option: {
      method: 'get',
    }
  });
};

/**
 * 执行转盘抽奖
 * @param retryCount 重试次数，默认为0
 * @param existingRequestId 现有的requestId，用于重试时保持不变
 * @returns 抽奖结果
 */
export const executeSpinningWheel = async (
  retryCount: number = 0, 
  existingRequestId?: string
): Promise<ResDataType<SpinResult> | undefined> => {
  const uid = userInfoDetail.getDetail().uid || -1;
  const requestId = existingRequestId || uuid().toUpperCase();
  const ts = Date.now();
  
  // 获取风控票据
  const ticket = await getXuidTicket({
    businessId: XUID_ticketConfig.coinTask.businessId,
    scene: XUID_ticketConfig.coinTask.scene,
    uid
  });
  
  // 生成签名: md5(requestId&uid&ticket&ts&retry&salt)
  const signatureData = `${requestId}&${uid}&${ticket}&${ts}&${retryCount}&${XUID_ticketConfig.coinTask.salt}`;
  const { checkData: signature } = await encryptByType('md5', {
    checkData: signatureData
  });
  
  const requestData: SpinRequest = {
    requestId,
    ts,
    retry: retryCount,
    ticket,
    signature
  };
  
  const response = await request<SpinResult>({
    ...API_ADSE,
    url: `incentive/ting/welfare/rewardRotaryTableAward/ts-${ts}`,
    option: {
      method: 'post',
      data: JSON.stringify(requestData),
      headers: {
        'Content-Type': 'application/json',
      },
    }
  });
  
  // 如果需要重试，递归调用
  if (response?.data?.retry && retryCount < 3) {
    console.log(`Lottery API requires retry, attempt ${retryCount + 1}`);
    return executeSpinningWheel(retryCount + 1, requestId);
  }
  
  return response;
};


