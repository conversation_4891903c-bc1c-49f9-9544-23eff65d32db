import { atom } from 'jotai';

export interface PrizeModalInfo {
  showRewardModal: boolean
  coins?: number
  modalType?: 'reward' | 'thanks',
  title?: string | undefined,
  isPhysicalPrize?: boolean,
  subTitle?: string | undefined,
  isFirstFree?: boolean
  cashText?: string
  awardCode?: string
}

const initialState: PrizeModalInfo = {
  coins: 0,
  showRewardModal: false,
  modalType: 'reward',
  isPhysicalPrize: false,
  isFirstFree: false,
  cashText: '',
  awardCode: '',
}
export const showPrizeModalAtom = atom<PrizeModalInfo>(initialState);

export {initialState};
