import React, { useRef } from "react";
import { View, Animated, Text, Image } from "react-native";
import { getStyles } from "./styles";
import { useAtomValue } from "jotai";
import rewardModalContentThemeAtom, { darkTheme, lightTheme } from "./theme";
import { BetterImage, Touch, FastImage } from "@xmly/rn-components";
import { preloadImages } from "utils/preloadImages";
import { themeAtom } from "atom/theme";
import ConfirmButton from "components/CoinCenter/common/ConfirmButton";

const THANK_YOU_PIC = 'https://imagev2.xmcdn.com/storages/2219-audiofreehighqps/2D/D9/GKwRIRwMQ6RsAAAgfQPdj_Pv.png';
const IPHONE_PIC = 'https://imagev2.xmcdn.com/storages/b08b-audiofreehighqps/EF/F5/GKwRIJIMQ6RsAAAymgPdj_OQ.png';
const COINS_PIC = 'https://imagev2.xmcdn.com/storages/0a2e-audiofreehighqps/37/23/GAqh9sAMQ6RsAAA1EAPdj_Mx.png'
const RELOAD_PRIZE = 'https://imagev2.xmcdn.com/storages/0661-audiofreehighqps/9C/E3/GAqhfD0MQ6RrAAABpAPdj_LT.png'

export const useLotteryImages = () => {
  const theme = useAtomValue(themeAtom);
  const PRELOAD_IMAGES = theme === 'dark' ? [
    darkTheme.popupImage,
    THANK_YOU_PIC,
    IPHONE_PIC,
    COINS_PIC,
    RELOAD_PRIZE,
  ] : [
    lightTheme.popupImage,
    THANK_YOU_PIC,
    IPHONE_PIC,
    COINS_PIC,
    RELOAD_PRIZE,
  ];

  return () => preloadImages(PRELOAD_IMAGES);
};


// 导出所有需要预加载的图片
export const PRELOAD_IMAGES = [
  THANK_YOU_PIC,
  IPHONE_PIC,
  COINS_PIC,
  RELOAD_PRIZE,
  darkTheme.popupImage,
  lightTheme.popupImage
];

interface RewardModalContentProps {
  coins: number;
  upgradeCoins: number;
  btnText?: string;
  title?: string;
  onPress: () => void;
  onClose: () => void;
  scaleAnim?: Animated.Value;
  closeBtnText?: string;
  isPhysicalPrize: boolean;
  isFirstFree: boolean;
  modalType: 'reward' | 'thanks',
}

export default function AdRewardModalContent({
  coins,
  upgradeCoins,
  btnText = '看广告翻倍领取',
  title = '恭喜获得',
  scaleAnim,
  onPress,
  onClose,
  closeBtnText = '直接领取',
  isPhysicalPrize = false,
  isFirstFree = false,
  modalType,
}: RewardModalContentProps) {
  const theme = useAtomValue(rewardModalContentThemeAtom);
  const styles = getStyles(theme);
  if(!modalType) return null
  // const scaleAnim = useRef(new Animated.Value(0)).current;
  return (
    <View style={styles.contentContainer}>
      <Animated.View
        style={[
          styles.popupImageContainer,
          {
            transform: [
              { scale: scaleAnim || 1 }
            ]
          }
        ]}
      >
        {/* Image组件可以直接使用预加载资源 */}
        <Image
          source={{ uri: theme.popupImage }}
          style={styles.popupImage}
        />

        <View style={styles.rewardContainer}>
          <Text style={styles.congratsText}>{title}<Text style={styles.highlightText}>{coins}</Text>金币</Text>
          <View style={styles.banner}>
            <BetterImage
              source={{ uri: modalType === 'thanks' ? THANK_YOU_PIC : isPhysicalPrize ? IPHONE_PIC : COINS_PIC }}
              style={styles.bannerIcon}
              imgHeight={114}
              imgWidth={216}
            />
            <View style={styles.coinContainer}>
              <View style={styles.textContainer}>
                <Text style={styles.coinText}>已获得</Text>
                <Text style={styles.coinText}>{coins}金币</Text>
              </View>
              <View style={styles.textContainer}>
                <Text style={styles.adText}>看广告领</Text>
                <Text style={styles.adText}>{upgradeCoins}金币</Text>
              </View>
            </View>
          </View>
        </View>
        <View style={styles.buttonContainer}>
          <ConfirmButton
            text={btnText}
            onPress={onPress}
            style={styles.confirmButton}
          />
          <Touch style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeText}>{closeBtnText}</Text>
          </Touch>
        </View>
      </Animated.View>
    </View>
  );
}