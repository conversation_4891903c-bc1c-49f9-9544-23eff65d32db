import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  congratsTextColor: '#FFFFFF',
  popupImage:'https://imagev2.xmcdn.com/storages/5c93-audiofreehighqps/39/FC/GAqhp50LzkB2AAByKgOUatvf.png',
  closeTextColor: '#FFFFFF',
};

export const lightTheme = {
  congratsTextColor: '#491414',
  popupImage:'https://imagev2.xmcdn.com/storages/a2f4-audiofreehighqps/0B/09/GKwRIaILzjf-AAB60wOUZXBb.png', 
  closeTextColor: '#3D3D3D',
};

const adRewardModalThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
});

export default adRewardModalThemeAtom; 