import React from 'react'
import { View, Animated, Text, Image, NativeModules } from 'react-native'
import { Touch } from '@xmly/rn-components'
import { px } from 'utils/px'
import { getStyles } from './styles'
import { useAtomValue } from 'jotai'
import rewardModalContentThemeAtom, { darkTheme, lightTheme } from './theme'
import { preloadImages } from 'utils/preloadImages'
import { themeAtom } from 'atom/theme'
import ConfirmButton from 'components/CoinCenter/common/ConfirmButton'

// 导出金币图片以便预加载
export const COINS_IMAGE = 'https://imagev2.xmcdn.com/storages/99ff-audiofreehighqps/8C/1E/GKwRIRwL05RWAAA0tAOXmR1U.png'
const THANK_YOU_PIC = 'https://imagev2.xmcdn.com/storages/2219-audiofreehighqps/2D/D9/GKwRIRwMQ6RsAAAgfQPdj_Pv.png'
const IPHONE_PIC = 'https://imagev2.xmcdn.com/storages/b08b-audiofreehighqps/EF/F5/GKwRIJIMQ6RsAAAymgPdj_OQ.png'
const COINS_PIC = 'https://imagev2.xmcdn.com/storages/0a2e-audiofreehighqps/37/23/GAqh9sAMQ6RsAAA1EAPdj_Mx.png'
const RELOAD_PRIZE = 'https://imagev2.xmcdn.com/storages/0661-audiofreehighqps/9C/E3/GAqhfD0MQ6RrAAABpAPdj_LT.png'

const ADRESS_URL = ''
const PRIZE_LIST_URL = ''

export const useLotteryImages = () => {
  const theme = useAtomValue(themeAtom)
  const PRELOAD_IMAGES =
    theme === 'dark' ? [darkTheme.popupImage, THANK_YOU_PIC, IPHONE_PIC, COINS_PIC, RELOAD_PRIZE] : [lightTheme.popupImage, THANK_YOU_PIC, IPHONE_PIC, COINS_PIC, RELOAD_PRIZE]

  return () => preloadImages(PRELOAD_IMAGES)
}

// 导出所有需要预加载的图片
export const PRELOAD_IMAGES = [COINS_IMAGE, darkTheme.popupImage, lightTheme.popupImage]

interface RewardModalContentProps {
  coins: number
  btnText?: string
  title?: string
  subTitle?: string
  onPress: () => void
  onReflesh: () => void
  scaleAnim?: Animated.Value
  awardCode?: string
  cashText?: string
  isPhysicalPrize: boolean
  isFirstFree: boolean
  modalType: 'reward' | 'thanks'
}

export default function PrizeModalContent({
  coins,
  btnText = '确定',
  title = '恭喜获得',
  subTitle,
  scaleAnim,
  onPress,
  onReflesh,
  isPhysicalPrize = false,
  isFirstFree = false,
  cashText,
  awardCode,
  modalType,
}: RewardModalContentProps) {
  const theme = useAtomValue(rewardModalContentThemeAtom)
  const styles = getStyles(theme)
  // const scaleAnim = useRef(new Animated.Value(0)).current;
  return (
    <View style={styles.contentContainer}>
      <Animated.View
        style={[
          styles.popupImageContainer,
          {
            transform: [{ scale: scaleAnim || 1 }],
          },
        ]}
      >
        {/* Image组件可以直接使用预加载资源 */}
        <Image
          source={{ uri: theme.popupImage }}
          style={styles.popupImage}
        />

        <View style={styles.rewardContainer}>
          <Text style={styles.congratsText}>{title}</Text>
          {subTitle && <Text style={styles.subTitleText}>{subTitle}</Text>}
          <View style={styles.imgContainer}>
            <Image
              source={{ uri: modalType === 'thanks' ? THANK_YOU_PIC : isPhysicalPrize ? IPHONE_PIC : COINS_PIC }}
              style={styles.coinIcon}
            />
            {!!cashText && <Text style={styles.cashTextIcon}>{cashText}</Text>}
          </View>
          {modalType !== 'thanks' && !isPhysicalPrize && <Text style={styles.coinsText}>+{coins}</Text>}
          {modalType !== 'thanks' && isPhysicalPrize && awardCode && <Text style={[styles.codeText, { marginTop: px(20) }]}>兑换码：{awardCode}</Text>}
          {modalType === 'thanks' && (
            <View>
              <Text style={[styles.thanksText, { marginTop: px(10) }]}>大奖不会轻易出现</Text>
              <Text style={styles.thanksText}>再换一次奖池试试手气吧！</Text>
            </View>
          )}
        </View>

        <ConfirmButton
          text={btnText}
          onPress={() => {
            if(modalType === 'reward' && isPhysicalPrize){
              NativeModules.Page.start(`${ADRESS_URL}?awardCode=${awardCode}`)
              return
            }
            onPress?.()
          }}
          style={styles.confirmButton}
        />

        {!isFirstFree && modalType === 'thanks' && (
          <Touch
            style={styles.subButton}
            onPress={() => {
              onReflesh?.()
            }}
          >
            <Text>奖品换一换</Text>
          </Touch>
        )}
        {modalType !== 'thanks' && isPhysicalPrize && awardCode && (
          <Touch
            style={styles.subButton}
            onPress={() => {
              NativeModules.Page.start(PRIZE_LIST_URL)
            }}
          >
            <Text>查看奖品</Text>
          </Touch>
        )}
      </Animated.View>
    </View>
  )
}
