import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  backgroundColor: 'rgba(0, 0, 0, 0.7)',
  congratsTextColor: '#FFFFFF',
  popupImage:'https://imagev2.xmcdn.com/storages/aa59-audiofreehighqps/00/CD/GKwRIJELzkB7AABqLwOUat8G.png',
  subTitleTextColor: '#8D8D91',
};

export const lightTheme = {
  backgroundColor: 'rgba(0, 0, 0, 0.7)',
  congratsTextColor: '#491414',
  popupImage:'https://imagev2.xmcdn.com/storages/1ecc-audiofreehighqps/A1/F8/GKwRIasLzjf-AABpEwOUZXCk.png',
  subTitleTextColor: 'rgba(44, 44, 60, 0.4)',
};

const lotteryModalThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
});

export default lotteryModalThemeAtom; 