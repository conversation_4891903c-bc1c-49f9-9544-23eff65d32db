import { StyleSheet, Platform } from "react-native";
import { px } from "utils/px";
import { darkTheme } from "./theme";


export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({

  contentContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
  },
  rewardContainer: {
    alignItems: 'center',
  },
  popupImageContainer: {
    width: px(241),
    // aspectRatio: 241 / 293,
    minHeight: px(293),
    alignItems: 'center',
  },
  popupImage: {
    width: px(241),
    // aspectRatio: 241 / 293,
    minHeight: px(293),
    position: 'absolute',
    left: 0,
    top: 0,
  },
  imgContainer: {
    position:'relative'
  },
  cashTextIcon: {
    paddingHorizontal: px(6),
    paddingVertical: px(2),
    borderRadius: px(8),
    borderBottomLeftRadius: 0,
    fontSize: px(12),
    fontWeight: '600',
    textAlign: 'center',
    display: 'flex',
    alignItems: 'center',
    color: '#FF4444',
    position: 'absolute',
    bottom: px(0),
    right: px(-30),
    backgroundColor: '#FFE2E3'
  },
  coinIcon: {
    width: px(96),
    aspectRatio: 96 / 75,
  },
  congratsText: {
    fontSize: px(22),
    lineHeight: px(28),
    color: theme.congratsTextColor,
    marginBottom: px(15),
    marginTop: px(30),
    fontWeight: 'bold',
    fontFamily: 'Microsoft YaHei',
  },
  subTitleText: {
    fontSize: px(16),
    lineHeight: px(16),
    color: theme.subTitleTextColor,
    marginBottom: px(5),
  },
  coinsText: {
    fontSize: px(38),
    fontWeight: 'bold',
    color: '#FF4444',
    fontFamily: 'XmlyNumber',
  },
  thanksText: {
    fontSize: px(11),
    color: 'rgba(80, 24, 24, 0.5)',
    textAlign: 'center',  
  },
  codeText: {
    fontSize: px(16),
    fontWeight: 'bold',
    color: '#491414',
  },
  confirmButton: {
    marginTop: px(20)
  },
  subButton: {
    color: '#F26868',
    fontSize: px(12),
    marginTop: px(10)
  }
});