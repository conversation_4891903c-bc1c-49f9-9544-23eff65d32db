// 中奖跑马灯组件
import React, { useRef, useState, useCallback, useEffect } from 'react'
import { View, Text, Animated, Image } from 'react-native'
import { px } from 'utils/px'
import xmlog from 'utilsV2/xmlog'
import { Winner } from './types'

interface WinnerMarqueeProps {
  winnerList: Winner[]
  style?: any
}

const WinnerMarquee: React.FC<WinnerMarqueeProps> = ({ winnerList, style }) => {
  const scrollY = useRef(new Animated.Value(0)).current
  const [containerHeight, setContainerHeight] = useState(0)
  const animationRef = useRef<Animated.CompositeAnimation | null>(null)

  // 默认头像
  const defaultAvatar = 'https://imagev2.xmcdn.com/storages/80dc-audiofreehighqps/09/BD/GKwRIJIMJdACAAAg9QPJ0CEk.png'

  // 处理中奖数据
  const processWinnerData = useCallback((winners: Winner[]) => {
    return winners.map(winner => ({
      ...winner,
      // 处理用户名脱敏显示
      displayName: winner.name.length > 6 
        ? `${winner.name.substring(0, 3)}***${winner.name.substring(winner.name.length - 2)}`
        : winner.name,
      // 格式化奖励文案
      displayAward: winner.award.replace('抽到', '获得')
    }))
  }, [])

  // 启动滚动动画
  const startScrolling = useCallback(() => {
    if (winnerList.length <= 1 || containerHeight <= 0) return
    
    const itemHeight = px(16) // 单个项目高度
    const totalHeight = winnerList.length * itemHeight
    
    // 从容器高度开始，滚动到负的总高度
    scrollY.setValue(0)
    
    animationRef.current = Animated.loop(
      Animated.sequence([
        Animated.timing(scrollY, {
          toValue: -totalHeight,
          duration: winnerList.length * 2000, // 每个项目显示2秒
          useNativeDriver: true,
        }),
        Animated.timing(scrollY, {
          toValue: 0,
          duration: 0, // 瞬间重置到顶部
          useNativeDriver: true,
        })
      ])
    )
    
    animationRef.current.start()
  }, [winnerList.length, containerHeight, scrollY])

  // 停止滚动动画
  const stopScrolling = useCallback(() => {
    if (animationRef.current) {
      animationRef.current.stop()
      animationRef.current = null
    }
  }, [])

  // 当数据或尺寸变化时重新启动动画
  useEffect(() => {
    if (winnerList.length > 0 && containerHeight > 0) {
      stopScrolling()
      setTimeout(() => {
        startScrolling()
      }, 1000) // 延迟1秒开始滚动
    }
    
    return () => {
      stopScrolling()
    }
  }, [winnerList, containerHeight, startScrolling, stopScrolling])

  // 跑马灯展示埋点
  const marqueeShowReport = useCallback((winnerCount: number) => {
    // xmlog.event(67706, 'slipPage', {
    //   currPage: 'welfareCenter',
    //   winnerCount: winnerCount.toString()
    // })
  }, [])

  // 跑马灯点击埋点
  const marqueeClickReport = useCallback((winner: Winner) => {
    // xmlog.click(67707, 'WinnerMarquee', {
    //   currPage: 'welfareCenter',
    //   winnerName: winner.name,
    //   award: winner.award
    // })
  }, [])

  // 组件挂载时发送展示埋点
  useEffect(() => {
    if (winnerList?.length > 0) {
      marqueeShowReport(winnerList?.length)
    }
  }, [winnerList?.length, marqueeShowReport])

  // 渲染中奖信息
  const renderWinnerItem = useCallback((winner: Winner, index: number) => {
    const processedWinner = processWinnerData([winner])[0]
    
    return (
      <View key={index} style={styles.winnerItem}>
        <Image 
          source={{ uri: winner.avatar || defaultAvatar }} 
          style={styles.avatar}
        />
        <Text style={styles.winnerText}>
          {processedWinner.displayName} {processedWinner.displayAward}
        </Text>
      </View>
    )
  }, [processWinnerData])

  // 如果没有中奖数据，不显示跑马灯
  if (!winnerList || winnerList.length === 0) {
    return null
  }

  return (
    <View 
      style={[styles.container, style]}
      onLayout={(e) => setContainerHeight(e.nativeEvent.layout.height)}
    >
      <Animated.View
        style={[
          styles.scrollContent,
          { transform: [{ translateY: scrollY }] }
        ]}
      >
        {winnerList.map(renderWinnerItem)}
      </Animated.View>
    </View>
  )
}

// 跑马灯样式
const styles = {
  container: {
    width: px(180), // 适配右侧布局的宽度
    height: px(20), // 单行显示的高度
    overflow: 'hidden' as const,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: px(12),
    paddingHorizontal: px(6),
    paddingVertical: px(2), // 减少内边距
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  
  scrollContent: {
    flexDirection: 'column' as const,
  },
  
  winnerItem: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    width: '100%',
    height: px(16), // 固定每个项目高度
    paddingVertical: px(0),
  },
  
  avatar: {
    width: px(12),
    height: px(12),
    borderRadius: px(6),
    marginRight: px(3),
  },
  
  winnerText: {
    fontSize: px(9),
    color: '#FF6B47',
    fontWeight: '500' as const,
    flex: 1,
    numberOfLines: 1,
  }
}

export default WinnerMarquee 