// SpiningWheel组件工具函数

import { WHEEL_CONFIG, PRIZE_POSITIONS, ANIMATION_CONFIG } from './constants'
import { Prize } from './types'

/**
 * 计算奖品在转盘上的位置
 * @param index 奖品索引 (0-7)
 * @param wheelRadius 转盘半径
 * @returns 奖品的x, y坐标
 */
export const calculatePrizePosition = (index: number, wheelRadius: number = WHEEL_CONFIG.WHEEL_RADIUS) => {
  const angle = (index * WHEEL_CONFIG.PRIZE_ANGLE - 90) * (Math.PI / 180) // 从12点方向开始
  const radius = wheelRadius * 0.7 // 奖品距离中心的距离
  
  const x = wheelRadius + radius * Math.cos(angle)
  const y = wheelRadius + radius * Math.sin(angle)
  
  return { x, y, angle: angle * (180 / Math.PI) }
}

/**
 * 根据奖品ID计算转盘应该旋转的角度
 * @param prizeId 中奖奖品ID (1-8)
 * @returns 旋转角度
 */
export const calculateRotationAngle = (prizeId: number): number => {
  // 确保prizeId在有效范围内
  const validPrizeId = Math.max(1, Math.min(8, prizeId))
  
  // 3x3网格布局的奖品位置对应的角度
  // 按照顺时针方向：左上 -> 上中 -> 右上 -> 右中 -> 右下 -> 下中 -> 左下 -> 左中
  const prizeAngles = [
    315, // 1: 左上 (5金币)
    0,   // 2: 上中 (10金币)  
    45,  // 3: 右上 (20金币)
    90,  // 4: 右中 (50金币)
    135, // 5: 右下 (1000金币)
    180, // 6: 下中 (500金币)
    225, // 7: 左下 (100金币)
    270, // 8: 左中 (30金币)
  ]
  
  // 获取目标角度
  const targetAngle = prizeAngles[validPrizeId - 1]
  
  // 添加随机的额外旋转圈数
  const extraRotations = ANIMATION_CONFIG.MIN_ROTATIONS + 
    Math.random() * (ANIMATION_CONFIG.MAX_ROTATIONS - ANIMATION_CONFIG.MIN_ROTATIONS)
  
  // 计算最终旋转角度 (逆时针旋转，所以是负值)
  const finalAngle = -(extraRotations * 360 + targetAngle)
  
  return finalAngle
}

/**
 * 验证奖品数据的有效性
 * @param prizes 奖品数组
 * @returns 是否有效
 */
export const validatePrizes = (prizes: Prize[]): boolean => {
  if (!Array.isArray(prizes) || prizes.length !== WHEEL_CONFIG.PRIZE_COUNT) {
    return false
  }
  
  return prizes.every(prize => 
    prize && 
    typeof prize.id === 'number' && 
    prize.id >= 1 && 
    prize.id <= WHEEL_CONFIG.PRIZE_COUNT &&
    typeof prize.amount === 'string'
  )
}

/**
 * 根据奖品ID查找奖品信息
 * @param prizes 奖品数组
 * @param prizeId 奖品ID
 * @returns 奖品信息或null
 */
export const findPrizeById = (prizes: Prize[], prizeId: number): Prize | null => {
  return prizes.find(prize => prize.id === prizeId) || null
}

/**
 * 生成转盘旋转的CSS动画样式
 * @param angle 旋转角度
 * @param duration 动画时长
 * @returns CSS动画样式对象
 */
export const generateSpinAnimation = (angle: number, duration: number = ANIMATION_CONFIG.SPIN_DURATION) => {
  return {
    transform: [{ rotate: `${angle}deg` }],
    duration,
    useNativeDriver: true,
  }
}

/**
 * 格式化奖品金额显示
 * @param amount 原始金额字符串
 * @returns 格式化后的金额字符串
 */
export const formatPrizeAmount = (amount: string): string => {
  // 移除多余的空格
  const cleanAmount = amount.trim()
  
  // 如果已经包含"金币"，直接返回
  if (cleanAmount.includes('金币')) {
    return cleanAmount
  }
  
  // 如果是纯数字，添加"金币"后缀
  if (/^\d+$/.test(cleanAmount)) {
    return `${cleanAmount}金币`
  }
  
  return cleanAmount
}

/**
 * 计算两个角度之间的最短旋转距离
 * @param from 起始角度
 * @param to 目标角度
 * @returns 最短旋转角度
 */
export const calculateShortestRotation = (from: number, to: number): number => {
  let diff = to - from
  
  // 确保角度在-180到180之间
  while (diff > 180) diff -= 360
  while (diff < -180) diff += 360
  
  return diff
}

/**
 * 生成随机的转盘旋转效果
 * @returns 随机旋转参数
 */
export const generateRandomSpin = () => {
  const rotations = ANIMATION_CONFIG.MIN_ROTATIONS + 
    Math.random() * (ANIMATION_CONFIG.MAX_ROTATIONS - ANIMATION_CONFIG.MIN_ROTATIONS)
  
  const randomAngle = Math.random() * 360
  
  return {
    rotations,
    angle: randomAngle,
    totalAngle: rotations * 360 + randomAngle
  }
}

/**
 * 检查设备是否支持动画
 * @returns 是否支持动画
 */
export const isAnimationSupported = (): boolean => {
  // 在React Native中，大部分设备都支持动画
  // 这里可以根据需要添加更复杂的检测逻辑
  return true
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param delay 延迟时间
 * @returns 防抖后的函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param delay 延迟时间
 * @returns 节流后的函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let lastCall = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      func(...args)
    }
  }
}

/**
 * 深度克隆对象
 * @param obj 要克隆的对象
 * @returns 克隆后的对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as unknown as T
  }
  
  const cloned = {} as T
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key])
    }
  }
  
  return cloned
}

/**
 * 生成唯一ID
 * @returns 唯一ID字符串
 */
export const generateUniqueId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 安全的JSON解析
 * @param jsonString JSON字符串
 * @param defaultValue 默认值
 * @returns 解析结果或默认值
 */
export const safeJsonParse = <T>(jsonString: string, defaultValue: T): T => {
  try {
    return JSON.parse(jsonString)
  } catch {
    return defaultValue
  }
} 