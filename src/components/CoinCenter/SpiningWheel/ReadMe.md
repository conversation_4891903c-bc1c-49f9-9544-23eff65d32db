# 九宫格抽奖组件 (SpinningWheel)

## 组件概述

九宫格抽奖组件是福利中心的核心功能模块，为用户提供每日免费抽奖机会和观看激励视频广告获得额外抽奖机会。组件采用3×3九宫格布局设计，支持动态奖品配置和流畅的格子高亮动画效果。

## 功能特性

- 🎯 **每日免费抽奖** - 用户每天可获得一次免费抽奖机会
- 📺 **激励视频广告** - 观看广告获得额外抽奖次数
- 🎪 **九宫格布局** - 3×3九宫格抽奖界面，8个奖品位置
- ✨ **格子高亮动画** - 依次高亮格子创建抽奖效果
- ⚙️ **动态配置** - 支持后端动态配置奖品内容和图标
- 🎁 **奖励机制** - 集成统一的奖励领取和弹窗展示
- 🎪 **中奖跑马灯** - 标题右侧展示实时中奖信息滚动

## 业务流程

根据产品流程图，组件的完整业务流程如下：

```mermaid
flowchart TD
    A[开始] --> B[FN]
    B --> C{在线状态检查}
    C -->|是| D[PN]
    C -->|否| E[客户端]
    
    D --> F{是否是今日第一次抽奖}
    F -->|是| G[客户端]
    F -->|否| H[PN弹出获得机会]
    
    H --> I{是否观看广告获得机会}
    I -->|是| J[提示用户观看激励视频]
    I -->|否| K[抽奖]
    
    J --> L[END]
    
    K --> M[展示抽奖结果]
    M --> N{是否有奖励}
    N -->|是| O[客户端]
    N -->|否| P[END]
    
    O --> Q[处理奖励]
    Q --> R[END]
```

### 核心流程说明

1. **初始化检查** - 检查用户在线状态和抽奖资格
2. **抽奖资格判断** - 判断是否为今日首次免费抽奖
3. **获得抽奖机会** - 通过观看激励视频获得额外机会
4. **执行抽奖** - 九宫格高亮动画 + 后端抽奖接口
5. **奖励处理** - 展示抽奖结果并处理奖励发放

## 组件结构

```
src/components/CoinCenter/SpiningWheel/
├── index.tsx                    # 主组件入口
├── WinnerMarquee.tsx           # 中奖跑马灯组件
├── types.ts                     # TypeScript类型定义  
├── constants.ts                 # 常量定义
├── utils.ts                     # 工具函数
├── styles.ts                    # 样式定义
├── store.ts                     # Jotai状态管理
├── theme.ts                     # 主题配置
└── ReadMe.md                    # 组件文档
```

## API 接口

### 1. 查询九宫格抽奖信息

```
GET /incentive/ting/welfare/queryRotaryTableInfo/ts-{timestamp}
```

**返回参数**
```json
{
  "responseId": 17748155021257,
  "ret": 0,
  "data": {
    "success": true,
    "code": 200,
    "firstFree": true,
    "title": "幸运八重礼",
    "costCoins": 10000,
    "buttonText": "首次免费",
    "winnerList": [
      {
        "name": "烟**雨",
        "award": "抽到3000金币"
      },
      {
        "name": "用户***123",
        "award": "抽到3000金币"
      },
      {
        "name": "用户***345",
        "award": "抽到3000金币"
      },
      {
        "name": "用户***1",
        "award": "抽到3000金币"
      }
    ],
    "prizes": [
      { 
        "id": 1, 
        "amountText": "谢谢参与", 
        "icon": "url", 
        "fallback": true, 
        "awardType": 0
      },
      { 
        "id": 2, 
        "cashText": "约0.2元", 
        "amountText": "10金币", 
        "icon": "url", 
        "awardType": 1
      },
      { 
        "id": 3, 
        "amountText": "20金币", 
        "icon": "url", 
        "awardType": 1
      },
      { 
        "id": 4, 
        "amountText": "30金币", 
        "icon": "url", 
        "awardType": 1
      },
      { 
        "id": 5, 
        "amountText": "50金币", 
        "icon": "url", 
        "awardType": 1
      },
      { 
        "id": 6, 
        "amountText": "100金币", 
        "icon": "url", 
        "awardType": 1
      },
      { 
        "id": 7, 
        "amountText": "500金币", 
        "icon": "url", 
        "awardType": 1
      },
      { 
        "id": 8, 
        "amountText": "1000金币", 
        "icon": "url", 
        "awardType": 1
      }
    ]
  }
}
```

**字段说明**
- `firstFree`: 是否为首次免费抽奖
- `title`: 抽奖活动标题
- `costCoins`: 消耗金币数量（付费抽奖时使用）
- `buttonText`: 抽奖按钮显示文案
- `winnerList`: 中奖用户展示列表（用于跑马灯展示）
- `prizes`: 奖品配置列表
  - `awardType`: 奖品类型（0-无奖品, 1-积分奖品, 2-实物奖品）
  - `fallback`: 是否为兜底奖品（谢谢参与）
  - `cashText`: 金币对应现金价值说明

### 中奖跑马灯设计

#### 功能说明
- **位置**: 位于组件标题右侧
- **内容**: 展示 `winnerList` 中的中奖用户信息
- **动画**: 从右向左滚动播放
- **样式**: 橙色文字，用户头像 + 中奖信息

#### 跑马灯样式规范
```typescript
// 跑马灯容器样式
const marqueeStyles = {
  container: {
    position: 'absolute',
    right: px(16),
    top: px(20),
    height: px(24),
    overflow: 'hidden',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: px(12),
    paddingHorizontal: px(8),
    paddingVertical: px(4),
  },
  
  // 滚动内容
  scrollContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  // 用户头像
  avatar: {
    width: px(16),
    height: px(16),
    borderRadius: px(8),
    marginRight: px(4),
  },
  
  // 中奖文字
  winnerText: {
    fontSize: px(12),
    color: '#FF6B47',
    fontWeight: '500',
    marginRight: px(16),
  }
}
```

#### 跑马灯实现逻辑
```typescript
// 跑马灯组件实现
const WinnerMarquee: React.FC<{ winnerList: Winner[] }> = ({ winnerList }) => {
  const scrollX = useRef(new Animated.Value(0)).current
  const [containerWidth, setContainerWidth] = useState(0)
  const [contentWidth, setContentWidth] = useState(0)

  // 启动滚动动画
  const startScrolling = useCallback(() => {
    if (contentWidth > containerWidth) {
      Animated.loop(
        Animated.timing(scrollX, {
          toValue: -contentWidth,
          duration: contentWidth * 50, // 根据内容长度调整速度
          useNativeDriver: true,
        })
      ).start()
    }
  }, [contentWidth, containerWidth])

  // 渲染中奖信息
  const renderWinnerItem = (winner: Winner, index: number) => (
    <View key={index} style={styles.winnerItem}>
      <Image 
        source={{ uri: winner.avatar || defaultAvatar }} 
        style={styles.avatar}
      />
      <Text style={styles.winnerText}>
        {winner.name} {winner.award}
      </Text>
    </View>
  )

  return (
    <View 
      style={styles.container}
      onLayout={(e) => setContainerWidth(e.nativeEvent.layout.width)}
    >
      <Animated.View
        style={[
          styles.scrollContent,
          { transform: [{ translateX: scrollX }] }
        ]}
        onLayout={(e) => setContentWidth(e.nativeEvent.layout.width)}
      >
        {winnerList.map(renderWinnerItem)}
      </Animated.View>
    </View>
  )
}
```

#### 跑马灯配置参数
- **滚动速度**: 50ms/px（可根据需要调整）
- **循环播放**: 内容滚动完成后自动重新开始
- **显示条件**: 当 `winnerList` 数组不为空时显示
- **最大宽度**: 不超过屏幕宽度的1/3
- **动画效果**: 使用 `useNativeDriver: true` 提升性能

#### 数据处理
```typescript
// 处理中奖数据
const processWinnerData = (winnerList: Winner[]) => {
  return winnerList.map(winner => ({
    ...winner,
    // 处理用户名脱敏显示
    displayName: winner.name.length > 6 
      ? `${winner.name.substring(0, 3)}***${winner.name.substring(winner.name.length - 2)}`
      : winner.name,
    // 格式化奖励文案
    displayAward: winner.award.replace('抽到', '获得')
  }))
}
```

### 2. 获取抽奖信息接口

服务端会根据以下概率和策略返回抽奖结果：

#### 基本概率配置
| 奖品 | 概率 | 说明 |
|------|------|------|
| 谢谢参与 | 20% | 兜底奖品 |
| 10金币 | 20% | 基础奖励 |
| 20金币 | 20% | 基础奖励 |
| 30金币 | 20% | 基础奖励 |
| 50金币 | 5% | 中等奖励 |
| 100金币 | 5% | 中等奖励 |
| 500金币 | 5% | 高级奖励 |
| 1000金币 | 5% | 最高奖励 |

#### 分层策略
服务端会根据用户行为自动调整中奖结果：
- **新用户保护**：新用户必中1000金币
- **抽奖频次奖励**：每天抽奖次数大于5次，必中1000金币
- **广告观看奖励**：每天观看广告刷新奖励大于5次，必中1000金币

### 3. 观看广告刷新奖励

```
POST https://adse.ximalaya.com/incentive/ting/welfare/rewardNonGoldCoins
```

**功能说明**: 观看广告后刷新奖励池，不提供额外抽奖机会

**流程**: 
1. 调用 `watchAd` 观看激励视频
2. 调用 `rewardNonGoldCoins` 刷新奖励（rewardType=2）
3. 重新请求抽奖信息

**广告参数**:
```typescript
{
  sourceName: 'SPINNING_WHEEL',
  rewardType: 'SPINNING_WHEEL',
  configPositionName: 'inspire_video_spinning_wheel_refresh_rn',
  extInfo: JSON.stringify({
    sourceName: 'SPINNING_WHEEL',
    action: 'refresh'
  })
}
```

**请求参数**:
```json
{
  "requestId": "9F82A20E-AB11-43E5-BE66-E8C21D874C9C",
  "adId": 111,
  "adResponseId": 111,
  "rewardType": 2,
  "ecpm": "W+xTCdzntkpNeqxagB4VGA==",
  "encryptType": "1",
  "fallbackReq": 1,
  "ts": 12132456543,
  "extMap": "",
  "retry": 0,
  "signature": ""
}
```

**字段说明**:
- `requestId`: 请求唯一标识
- `rewardType`: 奖励类型（1-瓜分现金补签操作, 2-获取抽奖机会）
- `ecpm`: 本次广告的预估ecpm（加密）
- `encryptType`: 加密类型
- `fallbackReq`: 兜底标识（1-异常兜底, 0-正常完成）
- `signature`: 签名验证 `md5(requestId&uid&ecpm&opType&encryptType&ts&retry&salt)`

**返回参数**:
```json
{
  "responseId": 4558611507,
  "ret": 0,
  "data": {
    "success": true,
    "failCode": 200,
    "toast": "刷新成功",
    "retry": false
  }
}
```

**接口调用示例**:
```typescript
await rewardNonGoldCoins({
  requestId: generateRequestId(),
  adId: res.adId,
  adResponseId: res.adResponseId,
  rewardType: 2, // 刷新奖励类型
  ecpm: res.ecpm,
  encryptType: res.encryptType,
  fallbackReq: res.fallbackReq,
  ts: Date.now(),
  extMap: JSON.stringify({
    sourceName: 'SPINNING_WHEEL',
    action: 'refresh'
  }),
  retry: 0,
  signature: generateSignature(params)
})
```

### 4. 执行抽奖并领取奖励

**流程**:
1. 开始九宫格高亮动画（3圈 + 停在中奖位置）
2. 动画完成后观看激励视频
3. 调用奖励接口获得奖品（rewardType=16）
4. 展示奖励弹窗

**广告参数**:
```typescript
{
  sourceName: 'SPINNING_WHEEL',
  rewardType: 'SPINNING_WHEEL',
  configPositionName: 'inspire_video_spinning_wheel_rn',
  extInfo: JSON.stringify({
    sourceName: 'SPINNING_WHEEL',
    prizeId: targetPrizeIndex + 1
  })
}
```

**奖励接口调用**:
```typescript
await rewardGoldCoin({
  rewardType: 16, // 九宫格抽奖奖励类型
  sourceName: AD_SOURCE.SPINNING_WHEEL,
  adId: res.adId,
  adResponseId: res.adResponseId,
  encryptType: res.encryptType,
  ecpm: res.ecpm,
  fallbackReq: res.fallbackReq,
  extMap: JSON.stringify({
    prizeId: targetPrizeIndex + 1
  })
})
```

## 类型定义

```typescript
// types.ts
export interface SpinningWheelInfo {
  success: boolean
  code: number
  firstFree: boolean
  title: string
  costCoins: number
  buttonText: string
  prizes: Prize[]
  winnerList: Winner[]
}

export interface Prize {
  id: number
  amountText: string
  icon: string
  awardType: number
  cashText?: string
  fallback?: boolean
}

export interface Winner {
  name: string
  award: string
  avatar?: string
}

export interface SpinResult {
  success: boolean
  code: number
  prizeId: number
  amount: string
  remainingChances: number
  toast?: string
  coins?: number
}

export interface SpiningWheelProps {
  onError?: (error: SpinError) => void
  onSpinStart?: () => void
  disabled?: boolean
  autoRefresh?: boolean
}

export interface SpinError {
  code: number
  message: string
  source: 'api' | 'ad' | 'animation' | 'reward' | 'spin'
  details?: any
}

export enum SpinStatus {
  IDLE = 'idle',
  SPINNING = 'spinning', 
  REFRESHING = 'refreshing',
  FINISHED = 'finished'
}
```

## 核心功能实现

### 九宫格高亮动画

```typescript
// 九宫格抽奖动画 - 高亮格子序列
const startGridAnimation = (targetPrizeIndex: number) => {
  setIsSpinning(true)
  setCurrentHighlightIndex(0)

  let currentIndex = 0
  let rounds = 0
  const totalRounds = 3 // 转3圈
  const maxRounds = totalRounds * 8 + targetPrizeIndex
  const baseSpeed = 100 // 基础速度

  const animateStep = () => {
    if (rounds >= maxRounds) {
      // 动画结束，停在中奖位置
      setTimeout(() => {
        setCurrentHighlightIndex(-1)
        setIsSpinning(false)
        claimReward(targetPrizeIndex)
      }, 500)
      return
    }

    setCurrentHighlightIndex(currentIndex)
    currentIndex = (currentIndex + 1) % 8
    rounds++

    // 动态调整速度，后面越来越慢
    const progress = rounds / maxRounds
    const speed = baseSpeed + progress * progress * 300

    setTimeout(animateStep, speed)
  }

  animateStep()
}
```

### 奖励领取机制

```typescript
// 领取奖励 - 集成 listenTask 奖励方式
const claimReward = async (targetPrizeIndex: number) => {
  try {
    // 1. 观看激励视频
    const res = await watchAd({
      sourceName: AD_SOURCE.SPINNING_WHEEL,
      rewardType: RewardType.SPINNING_WHEEL,
      configPositionName: 'inspire_video_spinning_wheel_rn',
      extInfo: JSON.stringify({
        sourceName: AD_SOURCE.SPINNING_WHEEL,
        prizeId: targetPrizeIndex + 1,
      }),
    })

    if (res.success) {
      // 2. 调用奖励接口 - 使用 rewardType=16
      const result = await rewardGoldCoin({
        rewardType: 16, // 九宫格抽奖奖励类型
        sourceName: AD_SOURCE.SPINNING_WHEEL,
        adId: res.adId,
        adResponseId: res.adResponseId,
        encryptType: res.encryptType,
        ecpm: res.ecpm,
        fallbackReq: res.fallbackReq,
        extMap: JSON.stringify({
          prizeId: targetPrizeIndex + 1,
        }),
      }, true)

      if (result?.success) {
        // 3. 显示奖励弹窗
        setModalInfo({
          visible: true,
          coins: result.coins || 0,
          title: '恭喜中奖！',
        })
        
        // 4. 更新状态
        setHasCompletedDailyFree(true)
        fetchSpinningWheel()
      }
    }
  } catch (error) {
    console.error('领取奖励失败:', error)
    onError?.({
      code: -1,
      message: '领取奖励失败',
      source: 'reward',
      details: error
    })
  }
}
```

### 刷新奖励池

```typescript
// 刷新奖励池 - 观看广告刷新
const handleRefreshReward = useThrottleCallback(async () => {
  try {
    setIsRefreshing(true)
    
    // 1. 观看激励视频
    const res = await watchAd({
      sourceName: AD_SOURCE.SPINNING_WHEEL,
      rewardType: RewardType.SPINNING_WHEEL,
      configPositionName: 'inspire_video_spinning_wheel_refresh_rn',
      extInfo: JSON.stringify({
        sourceName: AD_SOURCE.SPINNING_WHEEL,
        action: 'refresh'
      })
    })
    
    if (res.success) {
      // 2. 刷新奖励池 - 使用 rewardType=2
      const result = await rewardNonGoldCoins({
        requestId: generateRequestId(),
        adId: res.adId,
        adResponseId: res.adResponseId,
        rewardType: 2, // 刷新奖励类型
        ecpm: res.ecpm,
        encryptType: res.encryptType,
        fallbackReq: res.fallbackReq,
        ts: Date.now(),
        extMap: JSON.stringify({
          sourceName: AD_SOURCE.SPINNING_WHEEL,
          action: 'refresh'
        }),
        retry: 0,
        signature: generateSignature({
          requestId: result.requestId,
          uid: userInfo.uid,
          ecpm: res.ecpm,
          opType: 2,
          encryptType: res.encryptType,
          ts: result.ts,
          retry: 0
        })
      })
      
      if (result?.success) {
        fetchSpinningWheel()
        Toast.info(result.toast || '刷新成功！')
      } else if (result?.retry) {
        // 需要重试
        handleRefreshReward()
      }
    }
  } catch (error) {
    console.error('刷新奖励失败:', error)
    Toast.info('刷新奖励失败，请稍后重试')
  } finally {
    setIsRefreshing(false)
  }
})

// 生成请求ID
const generateRequestId = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16).toUpperCase()
  })
}

// 生成签名
const generateSignature = (params: {
  requestId: string
  uid: string
  ecpm: string
  opType: number
  encryptType: string
  ts: number
  retry: number
}) => {
  const { requestId, uid, ecpm, opType, encryptType, ts, retry } = params
  const salt = 'your_salt_key' // 从配置获取
  const signStr = `${requestId}&${uid}&${ecpm}&${opType}&${encryptType}&${ts}&${retry}&${salt}`
  return md5(signStr)
}
```

## 样式设计

### 九宫格布局

```typescript
// styles.ts - 九宫格样式
export const getStyles = (theme) => StyleSheet.create({
  // 外容器 - 310×310px
  container: {
    width: px(310),
    height: px(310),
    marginHorizontal: px(16),
    marginTop: px(18),
    marginBottom: px(20),
  },
  
  // 九宫格容器 - 288×288px
  gridContainer: {
    width: px(288),
    height: px(288),
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    alignContent: 'space-between',
    margin: px(11),
  },
  
  // 奖品格子 - 96×96px
  prizeItem: {
    width: px(96),
    height: px(96),
    backgroundColor: '#FAFAFA',
    borderWidth: px(0.5),
    borderColor: 'transparent',
    borderRadius: px(6),
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  // 高亮状态
  prizeItemHighlight: {
    backgroundColor: 'rgba(255, 226, 227, 1)',
    borderColor: 'rgba(217, 87, 87, 0.5)',
  },
  
  // 中心按钮
  centerButton: {
    position: 'absolute',
    top: px(96 + 11),
    left: px(96 + 11),
    width: px(96),
    height: px(96),
    justifyContent: 'center',
    alignItems: 'center',
  }
})
```

### 按钮状态

```typescript
// 动态按钮文案
const getCenterButtonText = () => {
  if (isSpinning) return "抽奖中..."
  if (isGettingChances) return "获取中..."
  if (hasCompletedDailyFree) return "看广告抽奖1次"
  return "每日首次\n免费抽奖"
}
```

## 使用方式

### 基础使用

```tsx
import React from 'react'
import { View } from 'react-native'
import SpiningWheel from 'components/CoinCenter/SpiningWheel'
import ModuleCard from 'components/CoinCenter/common/ModuleCard'

export default function CoinCenter() {
  return (
    <View>
      <ModuleCard>
        <SpiningWheel />
      </ModuleCard>
    </View>
  )
}
```

### 带跑马灯的完整使用

```tsx
import React from 'react'
import { View, Text } from 'react-native'
import SpiningWheel from 'components/CoinCenter/SpiningWheel'
import WinnerMarquee from 'components/CoinCenter/SpiningWheel/WinnerMarquee'
import ModuleCard from 'components/CoinCenter/common/ModuleCard'

export default function CoinCenter() {
  return (
    <View>
      <ModuleCard>
        {/* 标题区域 */}
        <View style={{ position: 'relative', paddingBottom: 20 }}>
          <Text style={{ fontSize: 18, fontWeight: 'bold' }}>
            幸运大转盘
          </Text>
          {/* 跑马灯组件 */}
          <WinnerMarquee 
            winnerList={[
              { name: '用户12345***', award: '抽到33000金币', avatar: 'https://example.com/avatar1.png' },
              { name: '烟**雨', award: '抽到3000金币' },
              { name: '用户***123', award: '抽到3000金币' }
            ]}
          />
        </View>
        
        {/* 九宫格抽奖组件 */}
        <SpiningWheel />
      </ModuleCard>
    </View>
  )
}
```

### 带错误处理

```tsx
import React from 'react'
import { Toast } from '@xmly/rn-sdk'
import SpiningWheel from 'components/CoinCenter/SpiningWheel'

export default function CoinCenter() {
  const handleSpinError = (error) => {
    console.error('抽奖失败:', error)
    
    switch (error.source) {
      case 'ad':
        Toast.fail('广告加载失败，请稍后重试')
        break
      case 'reward':
        Toast.fail('奖励领取失败，请稍后重试')
        break
      case 'api':
        Toast.fail('网络异常，请检查网络连接')
        break
      default:
        Toast.fail('抽奖失败，请稍后重试')
    }
  }

  return (
    <SpiningWheel
      onError={handleSpinError}
      onSpinStart={() => console.log('开始抽奖')}
    />
  )
}
```

## 状态管理

### Jotai 状态

```typescript
// store.ts
import { atom } from 'jotai'

// 九宫格抽奖信息状态
export const spinningWheelAtom = atom<SpinningWheelInfo>({
  success: false,
  code: 200,
  firstFree: true,
  title: '幸运八重礼',
  costCoins: 10000,
  buttonText: '首次免费',
  prizes: [],
  winnerList: []
})

// 获取抽奖信息
export const writeSpinningWheelAtom = atom(
  null,
  async (get, set) => {
    try {
      const response = await queryRotaryTableInfo()
      if (response?.data?.success) {
        set(spinningWheelAtom, response.data)
      }
    } catch (error) {
      console.error('Failed to fetch spinning wheel info:', error)
    }
  }
)
```

## 埋点统计

| 事件码 | 事件名 | 说明 | 参数 |
|--------|--------|------|------|
| 67701 | spinning_wheel_show | 九宫格模块展示 | currPage, moduleTitle, wheelTitle |
| 67703 | spinning_wheel_click | 抽奖按钮点击 | currPage, Item, firstFree, costCoins |
| 67704 | spinning_wheel_result | 抽奖结果 | currPage, prizeId, amount, success |
| 67705 | spinning_wheel_refresh | 刷新奖励池 | currPage, action, success |
| 67706 | winner_marquee_show | 跑马灯展示 | currPage, winnerCount |
| 67707 | winner_marquee_click | 跑马灯点击 | currPage, winnerName, award |

```typescript
// 埋点实现
const onShow = () => {
  xmlog.event(67701, 'slipPage', {
    currPage: 'welfareCenter',
    moduleTitle: '九宫格抽奖',
    wheelTitle: spinningWheelInfo.title
  })
}

const clickReport = (buttonText: string) => {
  xmlog.click(67703, 'SpinningWheel', {
    currPage: 'welfareCenter',
    Item: buttonText,
    firstFree: spinningWheelInfo.firstFree.toString(),
    costCoins: spinningWheelInfo.costCoins.toString()
  })
}

// 跑马灯埋点
const marqueeShowReport = (winnerCount: number) => {
  xmlog.event(67706, 'slipPage', {
    currPage: 'welfareCenter',
    winnerCount: winnerCount.toString()
  })
}

const marqueeClickReport = (winner: Winner) => {
  xmlog.click(67707, 'WinnerMarquee', {
    currPage: 'welfareCenter',
    winnerName: winner.name,
    award: winner.award
  })
}
```

## 常见问题

### Q: 九宫格不高亮？
**A:** 检查 `currentHighlightIndex` 状态和样式配置

### Q: 动画速度不对？
**A:** 调整 `baseSpeed` 和速度变化算法

### Q: 奖品图标不显示？
**A:** 检查图标URL有效性和网络连接

### Q: 首次免费状态不正确？
**A:** 验证接口返回的 `firstFree` 字段和本地状态管理

### Q: 广告观看后没有获得奖励？
**A:** 检查广告完整播放回调和奖励接口调用

### Q: 刷新奖励失败？
**A:** 确认使用 `rewardNonGoldCoins` 接口且 `rewardType=2`，检查签名生成是否正确

### Q: 抽奖奖励领取失败？
**A:** 确认使用 `rewardGoldCoin` 接口且 `rewardType=16`

### Q: 新用户没有中1000金币？
**A:** 检查服务端新用户识别逻辑和分层策略配置

### Q: 高频用户没有触发保底奖励？
**A:** 验证服务端抽奖次数和广告观看次数统计是否正确

### Q: 跑马灯不滚动？
**A:** 检查 `winnerList` 数据是否存在，容器宽度是否正确设置

### Q: 跑马灯滚动速度太快/太慢？
**A:** 调整动画 `duration` 参数，建议使用 `contentWidth * 50` 的计算方式

### Q: 跑马灯显示不完整？
**A:** 检查容器的 `overflow: 'hidden'` 设置和内容宽度计算

### Q: 用户头像不显示？
**A:** 检查 `avatar` 字段URL有效性，确保设置了默认头像

### Q: 接口签名验证失败？
**A:** 检查签名算法 `md5(requestId&uid&ecpm&opType&encryptType&ts&retry&salt)` 参数顺序和salt配置

### Q: 接口返回 retry=true？
**A:** 根据返回结果自动重试，保持 requestId 一致，递增 retry 参数

### Q: 刷新奖励后概率没变化？
**A:** 服务端会动态调整奖励池，建议重新请求抽奖信息接口

## 待解决疑问

以下问题需要与产品和后端确认：

### 1. 刷新奖励机制确认
**疑问**: 刷新奖励成功后是否需要重新请求抽奖信息接口来更新奖品列表？

**当前实现**: 
```typescript
if (result?.success) {
  fetchSpinningWheel() // 重新请求抽奖信息
  Toast.info(result.toast || '刷新成功！')
}
```

**需要确认**:
- 刷新奖励是否会改变 `prizes` 数组内容？
- 是否只是改变后端概率算法，前端奖品展示不变？
- 刷新成功后用户界面是否需要视觉反馈？

### 2. 兑换抽奖机会接口作用
**疑问**: 除了观看广告刷新奖励外，是否还有其他兑换抽奖机会的接口？

**需要确认**:
- 是否存在消耗金币兑换抽奖机会的接口？
- `costCoins` 字段的具体作用和使用场景
- 付费抽奖和免费抽奖的业务逻辑差异
- 是否需要实现多种获得抽奖机会的方式？

**相关字段**:
```json
{
  "costCoins": 10000, // 需要消耗的金币
  "firstFree": true,  // 首次免费
  "buttonText": "首次免费" // 按钮文案
}
```

### 3. 跑马灯用户头像缺失
**疑问**: 跑马灯中奖信息列表缺少用户头像数据

**当前数据结构**:
```json
{
  "winnerList": [
    {
      "name": "烟**雨",
      "award": "抽到3000金币"
      // 缺少 avatar 字段
    }
  ]
}
```

**需要确认**:
- 后端是否需要在 `winnerList` 中添加 `avatar` 字段？
- 如果没有真实头像，是否使用默认头像或用户名首字母头像？
- 跑马灯是否需要显示用户头像，还是只显示文字信息？

**建议的数据结构**:
```json
{
  "winnerList": [
    {
      "name": "烟**雨",
      "award": "抽到3000金币",
      "avatar": "https://example.com/avatar.png" // 新增字段
    }
  ]
}
```

### 4. 其他待确认问题

#### 4.1 抽奖次数限制
- 用户每日抽奖次数是否有上限？
- 免费抽奖和付费抽奖是否分别计数？
- 抽奖次数重置时间点（0点还是其他时间）？

#### 4.2 奖品发放机制
- 中奖后金币是否立即到账？
- 是否需要额外的领取确认步骤？
- 奖品发放失败的重试机制？

#### 4.3 UI交互细节
- 九宫格高亮动画的具体视觉效果要求
- 抽奖按钮在不同状态下的文案和样式
- 跑马灯滚动速度和展示时长的产品要求

#### 4.4 数据统计和埋点
- 是否需要统计用户抽奖行为数据？
- 中奖概率的A/B测试需求？
- 用户流失和转化的关键埋点？

---

**解决建议**:
1. 与产品经理确认业务逻辑和用户体验要求
2. 与后端开发确认接口数据结构和字段含义
3. 与设计师确认UI交互细节和视觉效果
4. 与数据分析师确认埋点统计需求

## 技术规范

### 代码风格
- 遵循项目统一的 TypeScript 规范
- 使用函数式组件和 Hooks
- 采用 Jotai 进行状态管理
- 统一使用 `getStyles(theme)` 模式

### 性能优化
- 使用 `useThrottleCallback` 防止重复点击
- 动画使用 `setTimeout` 而非 `setInterval`
- 合理的状态更新和重渲染控制

### 错误处理
- 完整的错误类型定义和分类处理
- 用户友好的错误提示
- 详细的错误日志记录

### 依赖说明
- `@xmly/rn-sdk` - Toast 提示
- `jotai` - 状态管理
- `utils/watchAd` - 广告观看
- `hooks/useRewardGoldCoin` - 奖励领取（rewardType=16）
- `hooks/useRewardNonGoldCoins` - 刷新奖励池（rewardType=2）
- `atom/listenTaskModal` - 奖励弹窗

### 奖励类型说明
- **rewardType=2**: 刷新奖励池，不发放金币奖励
- **rewardType=16**: 九宫格抽奖奖励，根据中奖结果发放对应金币

### 服务端概率算法
服务端会根据以下规则自动调整中奖概率：
1. **新用户识别**: 通过用户注册时间或首次抽奖标识判断
2. **抽奖频次统计**: 每日抽奖次数计数，超过5次触发高概率
3. **广告观看统计**: 每日通过广告刷新奖励次数，超过5次触发高概率
4. **分层中奖**: 满足条件的用户自动获得1000金币奖励
5. **奖励池刷新**: 观看广告后动态调整奖品概率分布