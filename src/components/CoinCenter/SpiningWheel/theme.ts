// SpiningWheel组件主题配置

import { atom } from 'jotai'
import { themeAtom } from 'atom/theme'
import { PRIZE_COLORS, PRIZE_COLORS_DARK, WHEEL_CONFIG } from './constants'
import { WheelTheme } from './types'

// 九宫格转盘主题类型
export interface GridTheme {
  // 宫格样式
  gridBackground: string;
  gridHighlightBackground: string;
  gridBorder: string;
  gridHighlightBorder: string;
  
  // 文字样式
  titleColor: string;
  titleHighlightColor: string;
  
  // 容器样式
  containerBackground: string;
  
  // 按钮样式
  buttonBackground: string;
  buttonTextColor: string;
  buttonDisabledBackground: string;
  
  // 其他通用样式
  subtitleColor: string;
  headerTextColor: string;
}

// 浅色主题配置
const lightTheme: WheelTheme = {
  // 转盘样式
  wheelBackground: '#FFFFFF',
  wheelBorder: '#E5E5E5',
  wheelSize: WHEEL_CONFIG.WHEEL_SIZE,
  
  // 奖品区域样式
  prizeBackground: PRIZE_COLORS,
  prizeTextColor: '#333333',
  prizeFontSize: 12,
  
  // 按钮样式
  buttonBackground: '#FF6B47',
  buttonTextColor: '#FFFFFF',
  buttonDisabledBackground: '#CCCCCC',
  
  // 指针样式
  pointerColor: '#FF4444',
  pointerSize: WHEEL_CONFIG.POINTER_SIZE,
}

// 深色主题配置
const darkTheme: WheelTheme = {
  // 转盘样式
  wheelBackground: '#2C2C2C',
  wheelBorder: '#404040',
  wheelSize: WHEEL_CONFIG.WHEEL_SIZE,
  
  // 奖品区域样式
  prizeBackground: PRIZE_COLORS_DARK,
  prizeTextColor: '#FFFFFF',
  prizeFontSize: 12,
  
  // 按钮样式
  buttonBackground: '#FF6B47',
  buttonTextColor: '#FFFFFF',
  buttonDisabledBackground: '#666666',
  
  // 指针样式
  pointerColor: '#FF4444',
  pointerSize: WHEEL_CONFIG.POINTER_SIZE,
}

// 主题原子，根据全局主题自动切换
export const spinningWheelThemeAtom = atom<WheelTheme>((get) => {
  const globalTheme = get(themeAtom)
  return globalTheme === 'dark' ? darkTheme : lightTheme
})

// 自定义主题原子，允许覆盖默认主题
export const customSpinningWheelThemeAtom = atom<Partial<WheelTheme> | null>(null)

// 最终主题原子，合并默认主题和自定义主题
export const finalSpinningWheelThemeAtom = atom<WheelTheme>((get) => {
  const defaultTheme = get(spinningWheelThemeAtom)
  const customTheme = get(customSpinningWheelThemeAtom)
  
  if (!customTheme) {
    return defaultTheme
  }
  
  return {
    ...defaultTheme,
    ...customTheme,
    // 确保数组类型的属性正确合并
    prizeBackground: customTheme.prizeBackground || defaultTheme.prizeBackground,
  }
})

// 主题工具函数
export const getThemeColors = (theme: WheelTheme) => ({
  primary: theme.buttonBackground,
  secondary: theme.wheelBorder,
  background: theme.wheelBackground,
  text: theme.prizeTextColor,
  disabled: theme.buttonDisabledBackground,
})

// 获取奖品背景色
export const getPrizeBackgroundColor = (theme: WheelTheme, index: number): string => {
  const colors = theme.prizeBackground
  return colors[index % colors.length] || colors[0]
}

// 主题预设
export const THEME_PRESETS = {
  light: lightTheme,
  dark: darkTheme,
  
  // 自定义主题预设
  colorful: {
    ...lightTheme,
    prizeBackground: [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
      '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'
    ],
    buttonBackground: '#E74C3C',
    pointerColor: '#E74C3C',
  } as WheelTheme,
  
  elegant: {
    ...lightTheme,
    wheelBackground: '#F8F9FA',
    wheelBorder: '#DEE2E6',
    prizeBackground: [
      '#E9ECEF', '#F8F9FA', '#E9ECEF', '#F8F9FA',
      '#E9ECEF', '#F8F9FA', '#E9ECEF', '#F8F9FA'
    ],
    prizeTextColor: '#495057',
    buttonBackground: '#6C757D',
    pointerColor: '#6C757D',
  } as WheelTheme,
  
  neon: {
    ...darkTheme,
    wheelBackground: '#1A1A1A',
    wheelBorder: '#00FF88',
    prizeBackground: [
      '#FF0080', '#00FF80', '#8000FF', '#FF8000',
      '#0080FF', '#FF0040', '#40FF00', '#8040FF'
    ],
    buttonBackground: '#00FF88',
    buttonTextColor: '#000000',
    pointerColor: '#00FF88',
  } as WheelTheme,
}

// 主题切换函数
export const switchTheme = (themeName: keyof typeof THEME_PRESETS) => {
  return THEME_PRESETS[themeName]
}

// 创建自定义主题
export const createCustomTheme = (baseTheme: WheelTheme, overrides: Partial<WheelTheme>): WheelTheme => {
  return {
    ...baseTheme,
    ...overrides,
    prizeBackground: overrides.prizeBackground || baseTheme.prizeBackground,
  }
}

// 主题验证函数
export const validateTheme = (theme: Partial<WheelTheme>): boolean => {
  const requiredKeys: (keyof WheelTheme)[] = [
    'wheelBackground', 'wheelBorder', 'wheelSize',
    'prizeBackground', 'prizeTextColor', 'prizeFontSize',
    'buttonBackground', 'buttonTextColor', 'buttonDisabledBackground',
    'pointerColor', 'pointerSize'
  ]
  
  return requiredKeys.every(key => key in theme)
}

// 主题颜色对比度检查
export const checkColorContrast = (backgroundColor: string, textColor: string): number => {
  // 简化的对比度计算，实际项目中可能需要更复杂的算法
  const bgLuminance = getLuminance(backgroundColor)
  const textLuminance = getLuminance(textColor)
  
  const lighter = Math.max(bgLuminance, textLuminance)
  const darker = Math.min(bgLuminance, textLuminance)
  
  return (lighter + 0.05) / (darker + 0.05)
}

// 获取颜色亮度
const getLuminance = (color: string): number => {
  // 简化的亮度计算
  const hex = color.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16) / 255
  const g = parseInt(hex.substr(2, 2), 16) / 255
  const b = parseInt(hex.substr(4, 2), 16) / 255
  
  return 0.299 * r + 0.587 * g + 0.114 * b
}

// 自动调整文字颜色以确保可读性
export const getReadableTextColor = (backgroundColor: string): string => {
  const luminance = getLuminance(backgroundColor)
  return luminance > 0.5 ? '#000000' : '#FFFFFF'
}

// 生成渐变色
export const generateGradientColors = (startColor: string, endColor: string, steps: number): string[] => {
  const colors: string[] = []
  
  for (let i = 0; i < steps; i++) {
    const ratio = i / (steps - 1)
    const color = interpolateColor(startColor, endColor, ratio)
    colors.push(color)
  }
  
  return colors
}

// 颜色插值
const interpolateColor = (color1: string, color2: string, ratio: number): string => {
  const hex1 = color1.replace('#', '')
  const hex2 = color2.replace('#', '')
  
  const r1 = parseInt(hex1.substr(0, 2), 16)
  const g1 = parseInt(hex1.substr(2, 2), 16)
  const b1 = parseInt(hex1.substr(4, 2), 16)
  
  const r2 = parseInt(hex2.substr(0, 2), 16)
  const g2 = parseInt(hex2.substr(2, 2), 16)
  const b2 = parseInt(hex2.substr(4, 2), 16)
  
  const r = Math.round(r1 + (r2 - r1) * ratio)
  const g = Math.round(g1 + (g2 - g1) * ratio)
  const b = Math.round(b1 + (b2 - b1) * ratio)
  
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
}

// 深色主题配置
const darkThemeGrid: GridTheme = {
  // 宫格样式
  gridBackground: 'rgba(255, 255, 255, 0.1)',
  gridHighlightBackground: '#FFE2E3',
  gridBorder: 'transparent',
  gridHighlightBorder: 'rgba(217, 87, 87, 0.5)',
  
  // 文字样式
  titleColor: 'rgba(255, 255, 255, 0.4)',
  titleHighlightColor: '#D95757',
  
  // 容器样式
  containerBackground: 'transparent',
  
  // 按钮样式
  buttonBackground: '#FF6B47',
  buttonTextColor: '#FFFFFF',
  buttonDisabledBackground: '#666666',
  
  // 其他通用样式
  subtitleColor: 'rgba(255, 255, 255, 0.6)',
  headerTextColor: '#FFFFFF',
};

// 浅色主题配置
const lightThemeGrid: GridTheme = {
  // 宫格样式
  gridBackground: '#FFFFFF',
  gridHighlightBackground: 'rgba(255, 226, 227, 1)',
  gridBorder: 'rgba(0, 0, 0, 0.1)',
  gridHighlightBorder: 'rgba(217, 87, 87, 0.5)',
  
  // 文字样式
  titleColor: '#333333',
  titleHighlightColor: '#FF6B47',
  
  // 容器样式
  containerBackground: '#FFFFFF',
  
  // 按钮样式
  buttonBackground: '#FF6B47',
  buttonTextColor: '#FFFFFF',
  buttonDisabledBackground: '#CCCCCC',
  
  // 其他通用样式
  subtitleColor: '#666666',
  headerTextColor: '#111111',
};

// 主题原子，根据全局主题自动切换
export const spinningWheelThemeGridAtom = atom<GridTheme>((get) => {
  const globalTheme = get(themeAtom);
  return globalTheme === 'dark' ? darkThemeGrid : lightThemeGrid;
});

export default spinningWheelThemeGridAtom; 