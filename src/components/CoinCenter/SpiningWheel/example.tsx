// SpiningWheel组件使用示例

import React from 'react'
import { View } from 'react-native'
import { Toast } from '@xmly/rn-sdk'
import SpiningWheel from './index'
import { SpinResult, SpinError } from './types'

// 基础使用示例
export const BasicExample: React.FC = () => {
  return (
    <View>
      <SpiningWheel />
    </View>
  )
}

// 带回调处理的示例
export const CallbackExample: React.FC = () => {
  const handlePrizeWon = (result: SpinResult) => {
    console.log('中奖结果:', result)
    Toast.success(`恭喜获得${result.amount}！`)
  }

  const handleSpinError = (error: SpinError) => {
    console.error('抽奖失败:', error)
    Toast.fail('抽奖失败，请稍后重试')
  }

  const handleSpinStart = () => {
    console.log('开始抽奖')
  }

  const handleSpinEnd = (result: SpinResult) => {
    console.log('抽奖结束:', result)
  }

  return (
    <SpiningWheel
      onPrizeWon={handlePrizeWon}
      onError={handleSpinError}
      onSpinStart={handleSpinStart}
      onSpinEnd={handleSpinEnd}
    />
  )
}

// 禁用状态示例
export const DisabledExample: React.FC = () => {
  return (
    <SpiningWheel
      disabled={true}
      onError={(error) => {
        console.log('组件已禁用:', error)
      }}
    />
  )
}

// 福利中心集成示例
export const WelfareCenterExample: React.FC = () => {
  const handlePrizeWon = (result: SpinResult) => {
    // 更新用户金币余额
    // updateUserBalance(result.amount)
    
    // 显示中奖动画
    // showPrizeAnimation(result)
    
    Toast.success(`恭喜获得${result.amount}！`)
  }

  return (
    <View style={{ padding: 20 }}>
      <SpiningWheel
        onPrizeWon={handlePrizeWon}
        onError={(error) => {
          Toast.fail('抽奖失败，请稍后重试')
        }}
        autoRefresh={true}
      />
    </View>
  )
}

// 导出所有示例
export default {
  BasicExample,
  CallbackExample,
  DisabledExample,
  WelfareCenterExample,
} 