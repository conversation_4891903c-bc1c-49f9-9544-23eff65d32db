// SpiningWheel组件状态管理

import { atom } from 'jotai'
import { DEFAULT_PRIZES, ERROR_CODES, ERROR_MESSAGES } from './constants'
import { validatePrizes } from './utils'
import { 
  querySpinningWheelInfo, 
  executeSpinningWheel, 
  SpinningWheelInfo, 
  SpinResult, 
  SpinStatus, 
  SpinError
} from 'services/welfare/spiningWheel'

// 初始转盘信息状态
const initialSpinningWheelInfo: SpinningWheelInfo = {
  success: false,
  code: 0,
  firstFree: true,
  title: '幸运八重礼',
  costCoins: 0,
  buttonText: '每日首次免费抽奖',
  prizes: DEFAULT_PRIZES,
  winnerList: []
}

// 转盘信息原子
export const spinningWheelAtom = atom<SpinningWheelInfo>(initialSpinningWheelInfo)

// 转盘状态原子
export const spinStatusAtom = atom<SpinStatus>(SpinStatus.IDLE)

// 抽奖结果原子
export const spinResultAtom = atom<SpinResult | null>(null)

// 错误状态原子
export const spinErrorAtom = atom<SpinError | null>(null)

// 加载状态原子
export const spinLoadingAtom = atom<boolean>(false)

// 旋转角度原子
export const rotationAngleAtom = atom<number>(0)

// 是否显示结果弹窗原子
export const showResultModalAtom = atom<boolean>(false)

export const refleshFlagAtom = atom<number>(0);

// 获取转盘信息的写入原子
export const writeSpinningWheelAtom = atom(
  null,
  async (get, set) => {
    try {
      set(spinLoadingAtom, true)
      set(spinErrorAtom, null)
      
      // 调用API获取转盘信息
      const response = await querySpinningWheelInfo()
      if (!response?.data) {
        throw new Error('API response error')
      }
      
      set(spinningWheelAtom, response.data)
    } catch (error) {
      console.error('Failed to fetch spinning wheel info:', error)
      
      const spinError: SpinError = {
        code: 1000,
        message: '获取转盘信息失败，请稍后重试',
        source: 'api',
        details: error
      }
      
      set(spinErrorAtom, spinError)
      set(spinningWheelAtom, { ...initialSpinningWheelInfo, success: false })
    } finally {
      set(spinLoadingAtom, false)
    }
  }
)

// 执行抽奖的写入原子
export const executeSpinAtom = atom(
  null,
  async (get, set) => {
    try {
      const currentInfo = get(spinningWheelAtom)
      
      // 检查是否有免费机会
      if (!currentInfo.firstFree && currentInfo.costCoins > 0) {
        const error: SpinError = {
          code: ERROR_CODES.NO_CHANCES,
          message: ERROR_MESSAGES[ERROR_CODES.NO_CHANCES],
          source: 'api'
        }
        set(spinErrorAtom, error)
        return
      }
      
      set(spinStatusAtom, SpinStatus.SPINNING)
      set(spinErrorAtom, null)
      set(spinLoadingAtom, true)
      
      // 调用抽奖API
      const response = await executeSpinningWheel()
      
      if (!response?.data) {
        throw new Error('API response error')
      }
      
      const result: SpinResult = {
        success: response.data.success,
        code: response.data.code,
        awardCode: response.data.awardCode,
        awardIndex: response.data.awardIndex,
        retry: response.data.retry,
        toast: response.data.toast
      }
      
      // 更新转盘信息
      set(spinningWheelAtom, {
        ...currentInfo,
        firstFree: false
      })
      
      set(spinResultAtom, result)
      set(spinStatusAtom, SpinStatus.FINISHED)
      
      // 延迟显示结果弹窗
      setTimeout(() => {
        set(showResultModalAtom, true)
      }, 500)
      
    } catch (error) {
      console.error('Failed to execute spin:', error)
      
      const spinError: SpinError = {
        code: 1001,
        message: '抽奖失败，请稍后重试',
        source: 'api',
        details: error
      }
      
      set(spinErrorAtom, spinError)
      set(spinStatusAtom, SpinStatus.IDLE)
    } finally {
      set(spinLoadingAtom, false)
    }
  }
)

// 重置转盘状态的写入原子
export const resetSpinStateAtom = atom(
  null,
  (get, set) => {
    set(spinStatusAtom, SpinStatus.IDLE)
    set(spinResultAtom, null)
    set(spinErrorAtom, null)
    set(rotationAngleAtom, 0)
    set(showResultModalAtom, false)
  }
)

// 设置旋转角度的写入原子
export const setRotationAngleAtom = atom(
  null,
  (get, set, angle: number) => {
    set(rotationAngleAtom, angle)
  }
)

// 关闭结果弹窗的写入原子
export const closeResultModalAtom = atom(
  null,
  (get, set) => {
    set(showResultModalAtom, false)
    // 可以在这里添加其他清理逻辑
  }
)

// 清除错误状态的写入原子
export const clearErrorAtom = atom(
  null,
  (get, set) => {
    set(spinErrorAtom, null)
  }
)

// 派生原子：是否可以抽奖
export const canSpinAtom = atom((get) => {
  const info = get(spinningWheelAtom)
  const status = get(spinStatusAtom)
  const loading = get(spinLoadingAtom)
  
  return info.success && info.firstFree && status === SpinStatus.IDLE && !loading
})

// 派生原子：是否正在旋转
export const isSpinningAtom = atom((get) => {
  const status = get(spinStatusAtom)
  return status === SpinStatus.SPINNING
})

// 派生原子：是否有错误
export const hasErrorAtom = atom((get) => {
  const error = get(spinErrorAtom)
  return error !== null
})

// 派生原子：当前按钮文本
export const buttonTextAtom = atom((get) => {
  const info = get(spinningWheelAtom)
  const status = get(spinStatusAtom)
  const loading = get(spinLoadingAtom)
  
  if (loading) {
    return '加载中...'
  }
  
  if (status === SpinStatus.SPINNING) {
    return '抽奖中...'
  }
  
  if (info.remainingChances <= 0) {
    return '明日再来'
  }
  
  return info.buttonText
})

// 派生原子：是否显示广告按钮
export const showAdButtonAtom = atom((get) => {
  const info = get(spinningWheelAtom)
  return info.success && info.remainingChances <= 0
})

// 状态持久化相关原子（可选）
export const persistedStateAtom = atom(
  (get) => ({
    spinningWheelInfo: get(spinningWheelAtom),
    lastFetchTime: Date.now()
  }),
  (get, set, newState: any) => {
    if (newState && newState.spinningWheelInfo) {
      set(spinningWheelAtom, newState.spinningWheelInfo)
    }
  }
)

// 自动刷新相关原子
export const autoRefreshAtom = atom<boolean>(true)

export const lastRefreshTimeAtom = atom<number>(0)

// 检查是否需要刷新的派生原子
export const needsRefreshAtom = atom((get) => {
  const autoRefresh = get(autoRefreshAtom)
  const lastRefreshTime = get(lastRefreshTimeAtom)
  const now = Date.now()
  
  // 如果开启自动刷新且距离上次刷新超过5分钟，则需要刷新
  return autoRefresh && (now - lastRefreshTime > 5 * 60 * 1000)
})

// 更新刷新时间的写入原子
export const updateRefreshTimeAtom = atom(
  null,
  (get, set) => {
    set(lastRefreshTimeAtom, Date.now())
  }
) 