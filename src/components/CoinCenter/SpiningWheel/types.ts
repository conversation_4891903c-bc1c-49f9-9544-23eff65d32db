// SpiningWheel组件类型定义
export interface SpinningWheelInfo {
  success: boolean
  code: number
  firstFree: boolean
  title: string
  costCoins: number
  buttonText: string
  prizes: Prize[]
  winnerList: Winner[]
}

export interface Prize {
  id: number
  amountText: string
  icon: string
  awardType: number
  cashText?: string
  fallback?: boolean
}

export interface Winner {
  name: string
  award: string
  avatar?: string
}

export interface SpinResult {
  success: boolean
  code: number
  prizeId: number
  amount: string
  remainingChances: number
  toast?: string
  coins?: number
}



export interface SpinError {
  code: number
  message: string
  source: 'api' | 'ad' | 'animation' | 'reward' | 'spin'
  details?: any
}

export interface SpiningWheelProps {
  onError?: (error: SpinError) => void
  onSpinStart?: () => void
  disabled?: boolean
  autoRefresh?: boolean
  theme?: Partial<WheelTheme>
}

export interface WheelTheme {
  // 九宫格样式
  gridBackground: string
  gridBorder: string
  gridHighlightBackground: string
  gridHighlightBorder: string
  
  // 文字样式
  titleColor: string
  titleHighlightColor: string
  headerTextColor: string
  subtitleColor: string
  
  // 按钮样式
  buttonBackground: string
  buttonTextColor: string
  buttonDisabledBackground: string
}

export interface AdInfo {
  adId: string
  adResponseId: string
  success: boolean
  ecpm?: string
  encryptType?: string
  fallbackReq?: number
}

// 转盘状态枚举
export enum SpinStatus {
  IDLE = 'idle',
  SPINNING = 'spinning',
  REFRESHING = 'refreshing',
  FINISHED = 'finished'
}

// 奖品类型枚举
export enum AwardType {
  NO_AWARD = 0,      // 无奖品
  COIN_AWARD = 1,    // 积分奖品
  PHYSICAL_AWARD = 2 // 实物奖品
}



// API响应类型
export interface ApiResponse<T> {
  responseId: number
  ret: number
  data: T
}

export interface QueryRotaryTableResponse {
  success: boolean
  code: number
  firstFree: boolean
  title: string
  costCoins: number
  buttonText: string
  prizes: Prize[]
  winnerList: Winner[]
}

export interface DrawRotaryTableResponse {
  success: boolean
  code: number
  prizeId: number
  amount: string
  remainingChances: number
  toast?: string
} 