// SpiningWheel组件主入口

import React, { useEffect } from 'react'
import { View, Text, TouchableOpacity, Image } from 'react-native'
import { useAtomValue, useSetAtom, useAtom } from 'jotai'
import { Toast } from '@xmly/rn-sdk'
import { px } from 'utils/px'
import { spinningWheelAtom, writeSpinningWheelAtom, refleshFlagAtom } from './store'
import { showPrizeModalAtom } from 'atom/spinningWheelModal'
import { getStyles } from './styles'
import { SpiningWheelProps } from './types'
import { spinningWheelThemeGridAtom } from './theme'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import { AD_SOURCE, RewardType, FallbackReqType, LISTEN_TASK_POSITION } from 'constants/ad'
import useRewardGoldCoin from 'hooks/useRewardGoldCoin'
import { balanceAtom } from "atom/welfare";
import watchAd from 'utils/watchAd'
import useThrottleCallback from 'hooks/useThrottleCallback'
import ModuleCard from '../common/ModuleCard'
import WinnerMarquee from './WinnerMarquee'
import { useNavigation } from '@react-navigation/native'

export const SpiningWheel: React.FC<SpiningWheelProps> = ({ onError, onSpinStart, disabled = false, autoRefresh = true }) => {
  const spinningWheelInfo = useAtomValue(spinningWheelAtom)
  const fetchSpinningWheel = useSetAtom(writeSpinningWheelAtom)
  const [refleshFlag, setRefleshFlag] = useAtom(refleshFlagAtom)
  const balance = useAtomValue(balanceAtom);
  const setModalInfo = useSetAtom(showPrizeModalAtom)
  const rewardGoldCoin = useRewardGoldCoin()
  const theme = useAtomValue(spinningWheelThemeGridAtom)
  const navigation = useNavigation()

  const [isSpinning, setIsSpinning] = React.useState(false)
  const [currentHighlightIndex, setCurrentHighlightIndex] = React.useState(-1)
  const [isRefreshing, setIsRefreshing] = React.useState(false)

  const styles = getStyles({
    gridBackground: '#FAFAFA',
    gridBorder: 'transparent',
    gridHighlightBackground: 'rgba(255, 226, 227, 1)',
    gridHighlightBorder: 'rgba(217, 87, 87, 0.5)',
    titleColor: '#333333',
    titleHighlightColor: '#FF6B47',
    headerTextColor: '#333333',
    subtitleColor: '#999999',
    buttonBackground: '#FF6B47',
    buttonTextColor: '#FFFFFF',
    buttonDisabledBackground: '#CCCCCC',
  })

  useEffect(() => {
    if (autoRefresh) {
      fetchSpinningWheel()
    }
  }, [autoRefresh, fetchSpinningWheel])

  // 空函数 - 埋点已注释
  const onShow = () => {}
  const clickReport = (buttonText: string) => {}
  
  // 实物奖品记录点击埋点
  const prizeRecordClickReport = () => {
    // xmlog.click(67708, 'prizeRecord', {
    //   currPage: 'welfareCenter',
    //   module: 'spinningWheel'
    // })
  }

  
  // 刷新奖励池 - 观看广告刷新
  const handleRefreshReward = useThrottleCallback(async () => {
    try {
      setIsRefreshing(true)
      clickReport('看广告刷新奖励')
      
      // 1. 观看激励视频
      const res = await watchAd({
        sourceName: AD_SOURCE.SPINNING_WHEEL,
        positionName: LISTEN_TASK_POSITION.positionName,
        slotId: LISTEN_TASK_POSITION.slotId,
        rewardType: RewardType.SPINNING_WHEEL,
        coins: 0,
        rewardVideoStyle: 0,
        configPositionName: 'inspire_video_spinning_wheel_refresh_rn',
        extInfo: JSON.stringify({
          sourceName: AD_SOURCE.SPINNING_WHEEL,
          action: 'refresh'
        })
      })
      
      if (res.success) {
        // 2. 使用 rewardGoldCoin 方法刷新奖励，isNoCoin 设为 true
        const result = await rewardGoldCoin({
          rewardType: 2, // 刷新奖励类型
          sourceName: AD_SOURCE.SPINNING_WHEEL,
          coins: 0,
          adId: res.adId,
          adResponseId: res.adResponseId,
          encryptType: res.encryptType,
          ecpm: res.ecpm,
          fallbackReq: res.fallbackReq || FallbackReqType.NORMAL,
          extMap: JSON.stringify({
            sourceName: AD_SOURCE.SPINNING_WHEEL,
            action: 'refresh'
          }),
          isNoCoin: true // 告诉服务器用户已经刷新奖励，不需要发放金币
        }, true) // 使用自定义奖励提示
        
        if (result?.success) {
          // 3. 重新调用 queryRotaryTableInfo 接口获取奖励信息
          fetchSpinningWheel()
          Toast.info(result.toast || '刷新成功！')
          
          // 刷新成功埋点已注释
        } else if (result?.retry) {
          // 需要重试
          setTimeout(() => {
            handleRefreshReward()
          }, 1000)
        } else {
          Toast.info('刷新奖励失败')
          // 刷新失败埋点已注释
        }
      }
    } catch (error) {
      console.error('刷新奖励失败:', error)
      Toast.info('刷新奖励失败，请稍后重试')
      
      if (onError) {
        onError({
          code: -1,
          message: '刷新奖励失败',
          source: 'api',
          details: error
        })
      }
    } finally {
      setIsRefreshing(false)
    }
  })

  // useEffect(() => {
  //   handleRefreshReward()
  // }, [refleshFlag])

  // 九宫格抽奖动画 - 高亮格子序列
  const startGridAnimation = (targetPrizeIndex: number) => {
    setIsSpinning(true)
    setCurrentHighlightIndex(0)

    let currentIndex = 0
    let rounds = 0
    const totalRounds = 3 // 转3圈
    const maxRounds = totalRounds * 8 + targetPrizeIndex
    const baseSpeed = 100 // 基础速度

    const animateStep = () => {
      if (rounds >= maxRounds) {
        // 动画结束，停在中奖位置
        setTimeout(() => {
          setCurrentHighlightIndex(-1)
          setIsSpinning(false)
          claimReward(targetPrizeIndex)
        }, 500)
        return
      }

      setCurrentHighlightIndex(currentIndex)
      currentIndex = (currentIndex + 1) % 8
      rounds++

      // 动态调整速度，后面越来越慢
      const progress = rounds / maxRounds
      const speed = baseSpeed + progress * progress * 300

      setTimeout(animateStep, speed)
    }

    animateStep()
  }

  // 领取奖励 - 显示中奖结果
  const claimReward = async (targetPrizeIndex: number) => {
    try {
      // 获取中奖奖品信息
      const prize = spinningWheelInfo.prizes[targetPrizeIndex]
      
      if (prize) {
        // 显示奖励弹窗
        setModalInfo({
          showRewardModal: true,
          coins: parseInt(prize.amountText.replace(/[^\d]/g, '')) || 0,
          title: '恭喜中奖！',
        })
        
        // 更新状态 - 重新获取转盘信息
        fetchSpinningWheel()

        // 抽奖结果埋点已注释
      }
    } catch (error) {
      console.error('领取奖励失败:', error)
      Toast.info('获取奖励失败，请稍后重试')
      
      if (onError) {
        onError({
          code: -1,
          message: '领取奖励失败',
          source: 'reward',
          details: error
        })
      }
    }
  }

  // 处理抽奖
  const handleSpin = async () => {
    if (disabled || isSpinning) return

    try {
      clickReport(getCenterButtonText())

      if (onSpinStart) {
        onSpinStart()
      }

      // 如果不是首次免费抽奖，需要检查金币是否足够
      if (!spinningWheelInfo.firstFree && spinningWheelInfo.costCoins > 0) {
        if(balance.coins < 10000) {
          Toast.info('金币不足，快去攒金币吧') // TODO: 金币数不足文案
          return
        }
      }

      // 调用新的抽奖API
      const { executeSpinningWheel } = await import('services/welfare/spiningWheel')
      const result = await executeSpinningWheel()

      if (result?.data?.success) {
        // 使用服务端返回的中奖位置
        const targetPrizeIndex = result.data.awardIndex - 1 // API返回1-8，需要转换为0-7
        
        // 开始九宫格动画
        startGridAnimation(targetPrizeIndex)
      } else {
        Toast.info(result?.data?.toast || '抽奖失败，请稍后重试')
        
        if (onError) {
          onError({
            code: result?.data?.code || -1,
            message: result?.data?.toast || '抽奖失败',
            source: 'api',
            details: result
          })
        }
      }
    } catch (error) {
      console.error('抽奖失败:', error)
      Toast.info('抽奖失败，请稍后重试')

      if (onError) {
        onError({
          code: -1,
          message: '抽奖失败',
          source: 'spin',
          details: error,
        })
      }
    }
  }

  // 计算奖品位置 - 按照设计图中的3x3网格布局
  const getPrizePosition = (index: number) => {
    const positions = [
      { row: 0, col: 0 }, // 谢谢参与 - 左上
      { row: 0, col: 1 }, // 10金币 - 上中
      { row: 0, col: 2 }, // 20金币 - 右上
      { row: 1, col: 2 }, // 30金币 - 右中
      { row: 2, col: 2 }, // 50金币 - 右下
      { row: 2, col: 1 }, // 100金币 - 下中
      { row: 2, col: 0 }, // 500金币 - 左下
      { row: 1, col: 0 }, // 1000金币 - 左中
    ]

    const position = positions[index]
    const itemWidth = px(96)
    const itemHeight = px(96)
    const spacing = px(11)

    return {
      left: position.col * (itemWidth + spacing),
      top: position.row * (itemHeight + spacing),
      width: itemWidth,
      height: itemHeight,
    }
  }

  // 渲染奖品项
  const renderPrizeItem = (prize: any, index: number) => {
    const position = getPrizePosition(index)
    const isHighlighted = currentHighlightIndex === index

    return (
      <View
        key={prize.id}
        style={[
          {
            position: 'absolute',
            left: position.left,
            top: position.top,
            width: position.width,
            height: position.height,
            borderWidth: px(0.5),
            borderColor: isHighlighted ? theme.gridHighlightBorder : theme.gridBorder,
            borderStyle: 'solid',
            borderRadius: px(6),
            alignItems: 'center',
            justifyContent: 'space-between',
            backgroundColor: isHighlighted ? theme.gridHighlightBackground : theme.gridBackground,
            paddingVertical: px(8),
            paddingHorizontal: px(4),
          },
        ]}
      >
        {/* 奖品金额文字在上方 */}
        <Text
          style={{
            fontSize: px(12),
            color: isHighlighted ? theme.titleHighlightColor : theme.titleColor,
            fontWeight: isHighlighted ? '600' : '500',
            textAlign: 'center',
            marginTop: px(2),
          }}
        >
          {prize.amountText}
        </Text>

        {/* 金币图标在下方 */}
        <Image
          source={{ uri: prize.icon }}
          style={{
            width: px(66.78),
            height: px(52),
            marginBottom: px(2),
          }}
          resizeMode="contain"
        />
      </View>
    )
  }

  if (!spinningWheelInfo.success) return null

  // 获取中心按钮文案
  const getCenterButtonText = () => {
    if (isSpinning) {
      return '抽奖中...'
    }
    if (isRefreshing) {
      return '刷新中...'
    }
    if (!spinningWheelInfo.firstFree) {
      return '10000金币抽大奖'
    }
    return spinningWheelInfo.buttonText
  }

  return (
    <ScrollAnalyticComp
      itemKey={'SpinningWheel'}
      onShow={onShow}
    >
      <ModuleCard
        style={{
          ...styles.container,
        }}
      >
        {/* 标题区域 - 左右布局 */}
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingBottom: px(16),
            paddingHorizontal: px(0),
          }}
        >
          {/* 左侧标题容器 */}
          <View style={{ flex: 1, alignItems: 'flex-start' }}>
            <Text style={{ fontSize: px(18), fontWeight: 'bold', color: theme.headerTextColor }}>
              幸运大转盘
            </Text>
          </View>
          
          {/* 右侧跑马灯容器 */}
          <View style={{ flex: 1, alignItems: 'flex-end' }}>
            <WinnerMarquee winnerList={spinningWheelInfo.winnerList || []} />
          </View>
        </View>

        {/* 实物奖品记录链接 */}
        <TouchableOpacity
          style={{
            alignSelf: 'flex-start',
            marginBottom: px(20),
            marginLeft: px(11), // 与九宫格左侧对齐
          }}
          onPress={() => {
            prizeRecordClickReport()
            navigation.navigate('RewardRecord')
          }}
          activeOpacity={0.6}
        >
          <Text
            style={{
              fontSize: px(14),
              color: theme.titleHighlightColor,
              fontWeight: '400',
              textDecorationLine: 'underline',
            }}
          >
            实物奖品记录
          </Text>
        </TouchableOpacity>

        {/* 九宫格区域 */}
        <View style={{ alignItems: 'center' }}>
            {/* 九宫格奖品容器 */}
            <View
              style={{
                width: px(310), // 310 - 2 * 11 = 288
                height: px(310), // 310 - 2 * 11 = 288
                position: 'relative',
                margin: px(11),
              }}
            >
              {/* 奖品项 */}
              {spinningWheelInfo.prizes.map((prize, index) => renderPrizeItem(prize, index))}
            </View>

            {/* 中心按钮 */}
            <View
              style={{
                position: 'absolute',
                left: 0,
                right: 0,
                top: 0,
                bottom: 0,
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 10,
              }}
            >
              <TouchableOpacity
                style={{
                  width: px(96),
                  height: px(96),
                  borderRadius: px(6),
                  backgroundColor: 'transparent',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
                onPress={handleSpin}
                disabled={isSpinning || isRefreshing}
                activeOpacity={0.8}
              >
                {/* 按钮背景图片 */}
                <Image
                  source={{ uri: 'https://imagev2.xmcdn.com/storages/80dc-audiofreehighqps/09/BD/GKwRIJIMJdACAAAg9QPJ0CEk.png' }}
                  style={{
                    position: 'absolute',
                    width: px(96),
                    height: px(96),
                    borderRadius: px(6),
                  }}
                  resizeMode="cover"
                />
                {/* 按钮文字 */}
                <Text
                  style={{
                    fontSize: px(12),
                    color: theme.buttonTextColor,
                    fontWeight: '600',
                    textAlign: 'center',
                    lineHeight: px(16),
                    zIndex: 1,
                  }}
                >
                  {getCenterButtonText()}
                </Text>
              </TouchableOpacity>
            </View>
        </View>

        {/* 底部提示文案 */}
        {!spinningWheelInfo.firstFree ? (
          <TouchableOpacity
            style={{
              width: px(311),
              height: px(44),
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              paddingVertical: px(8),
              borderRadius: px(6),
              backgroundColor: 'rgba(255, 68, 68, 0.08)',
              marginTop: px(16),
              alignSelf: 'center',
            }}
            onPress={() => {
              setRefleshFlag(refleshFlag + 1)
            }}
            disabled={isRefreshing}
            activeOpacity={0.8}
          >
            <Text
              style={{
                fontSize: px(12),
                fontWeight: '500',
                textAlign: 'center',
                color: '#F26868',
              }}
            >
              🎁 奖品换一换
            </Text>
            <Text
              style={{
                fontSize: px(8),
                fontWeight: '300',
                textAlign: 'center',
                color: '#F26868',
              }}
            >
              大奖已就位，就差你刷新这一下！
            </Text>
          </TouchableOpacity>
        ) : (
          <Text
            style={{
              fontSize: px(12),
              color: theme.subtitleColor,
              textAlign: 'center',
              marginTop: px(16),
            }}
          >
            每日首抽后升级奖池，解锁隐藏大奖！
          </Text>
        )}
      </ModuleCard>
    </ScrollAnalyticComp>
  )
}

export default SpiningWheel
