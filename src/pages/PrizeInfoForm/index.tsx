import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  ScrollView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ThemeContext } from '../../contextV2/themeContext';
import { PrizeInfoFormData } from './types';
import { getStyles } from './styles';
import { RootStackParamList } from '../../router/type';
import { StackScreenProps } from '@react-navigation/stack';
import BackBtn from '../../componentsV2/common/BackBtn';
import prize_info_arrow from '../../appImagesV2/prize_info_arrow';

export default function PrizeInfoForm(props: StackScreenProps<RootStackParamList>) {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const theme = useContext(ThemeContext);
  const styles = getStyles(theme);
  
  // 获取路由参数
  const { awardCode } = props.route.params as {
    awardCode: string;
    productName: string;
    coverPic: string;
  };
  

  const paddingTop = 10 + insets.top;

  const [formData, setFormData] = useState<PrizeInfoFormData>({
    realName: '',
    phoneNumber: '',
    province: '',
    city: '',
    district: '',
    detailAddress: '',
  });

  const handleSubmit = () => {
    // 验证表单
    if (!formData.realName.trim()) {
      Alert.alert('提示', '请输入真实姓名');
      return;
    }

    if (!formData.phoneNumber.trim()) {
      Alert.alert('提示', '请输入手机号');
      return;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.phoneNumber)) {
      Alert.alert('提示', '请输入正确的手机号');
      return;
    }

    if (!formData.province || !formData.city || !formData.district) {
      Alert.alert('提示', '请选择省市区');
      return;
    }

    if (!formData.detailAddress.trim()) {
      Alert.alert('提示', '请输入详细地址');
      return;
    }

    // TODO: 调用提交接口
    Alert.alert('提交成功', '您的信息已提交，我们会尽快处理', [
      {
        text: '确定',
        onPress: () => navigation.goBack(),
      },
    ]);
  };

  const handleRegionSelect = () => {
    // 这里应该打开地区选择器
    Alert.alert('提示', '地区选择功能待实现');
  };

  const updateFormData = (field: keyof PrizeInfoFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const getRegionText = () => {
    if (formData.province && formData.city && formData.district) {
      return `${formData.province} ${formData.city} ${formData.district}`;
    }
    return '';
  };

  return (
    <View style={styles.container}>
      {/* 头部 */}
      <View style={[styles.header, { paddingTop }]}>
        <View style={[styles.backBtn, { top: paddingTop }]}>
          <BackBtn onPress={navigation.goBack} />
        </View>
        <Text style={styles.title}> </Text>
      </View>

      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* 页面标题 */}
          <View>
            <Text style={styles.pageTitleText}>请填写中奖信息</Text>
          </View>

          {/* 兑换码 */}
          <View style={styles.formItemWithBorder}>
            <Text style={styles.label}>兑换码</Text>
            <Text style={styles.codeValue}>{awardCode}</Text>
          </View>

          {/* 真实姓名 */}
          <View style={styles.formItem}>
            <Text style={styles.label}>真实姓名</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.textInput}
                placeholder="请输入"
                placeholderTextColor="#CECECE"
                value={formData.realName}
                onChangeText={(text) => updateFormData('realName', text)}
                maxLength={20}
              />
            </View>
          </View>

          {/* 手机号 */}
          <View style={styles.formItem}>
            <Text style={styles.label}>手机号</Text>
            <View style={styles.phoneInputContainer}>
              <Text style={styles.phonePrefix}>+86</Text>
              <View style={styles.phoneSeparator} />
              <TextInput
                style={styles.textInput}
                placeholder="请输入"
                placeholderTextColor="#CECECE"
                value={formData.phoneNumber}
                onChangeText={(text) => updateFormData('phoneNumber', text)}
                keyboardType="numeric"
                maxLength={11}
              />
            </View>
          </View>

          {/* 省市区 */}
          <View style={styles.formItem}>
            <Text style={styles.label}>省市区</Text>
            <TouchableOpacity
              style={styles.regionSelector}
              onPress={handleRegionSelect}
              activeOpacity={1}
            >
              <Text style={styles.regionText}>
                {getRegionText() || '请选择'}
              </Text>
              <Image source={prize_info_arrow} style={styles.arrowIcon} />
            </TouchableOpacity>
          </View>

          {/* 详细地址 */}
          <View style={styles.formItem}>
            <Text style={styles.label}>详细地址</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.textInput}
                placeholder="请输入"
                placeholderTextColor="#CECECE"
                value={formData.detailAddress}
                onChangeText={(text) => updateFormData('detailAddress', text)}
                multiline
                textAlignVertical="top"
              />
            </View>
          </View>
        </View>

        {/* 警告提示 */}
        <View style={styles.warning}>
          <Text style={styles.warningText}>
            信息提交后将无法修改，请您在提交前确认各项信息准确无误
          </Text>
        </View>

        {/* 提交按钮 */}
        <TouchableOpacity
          style={styles.submitButton}
          onPress={handleSubmit}
          activeOpacity={1}
        >
          <Text style={styles.submitButtonText}>提交</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
} 