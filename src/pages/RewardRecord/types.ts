import { AwardInfo, AwardStatus, QueryMyRotaryTableAwardResponse } from '../../services/welfare/rewardRecord';

// 状态文本映射
export const getStatusText = (status: AwardStatus): string => {
  switch (status) {
    case AwardStatus.CLAIMING:
      return '领取中';
    case AwardStatus.UNCLAIMED:
      return '未兑换';
    case AwardStatus.CLAIMED:
      return '已兑换';
    case AwardStatus.SHIPPED:
      return '已发货';
    case AwardStatus.DELIVERED:
      return '已签收';
    case AwardStatus.EXPIRED:
      return '已过期';
    default:
      return '未知状态';
  }
};

// 状态类型映射
export const getStatusType = (status: AwardStatus): 'pending' | 'shipped' | 'delivered' | 'cancelled' => {
  switch (status) {
    case AwardStatus.CLAIMING:
    case AwardStatus.UNCLAIMED:
      return 'pending';
    case AwardStatus.SHIPPED:
      return 'shipped';
    case AwardStatus.CLAIMED:
    case AwardStatus.DELIVERED:
      return 'delivered';
    case AwardStatus.EXPIRED:
      return 'cancelled';
    default:
      return 'pending';
  }
};

// UI组件使用的奖励记录类型（直接对应接口返回）
export interface RewardRecord {
  orderNo: string;
  name: string;
  coverPic: string;
  createTime: string;
  expireTime: string;
  awardCode: string;
  expressNo?: string;
  expressLink?: string;
  status: number; // 1-领取中 2-未兑换 5-已兑换 6-已发货 7-已签收 8-已过期
}

// 重新导出查询响应类型
export type RewardRecordResponse = QueryMyRotaryTableAwardResponse;

// 保留原有的状态类型以便兼容
export interface RewardRecordOldResponse {
  code: number;
  message: string;
  data: {
    list: RewardRecord[];
    hasMore: boolean;
  };
}
