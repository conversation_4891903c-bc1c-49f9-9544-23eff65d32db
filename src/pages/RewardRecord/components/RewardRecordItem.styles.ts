import { StyleSheet } from 'react-native';
import { px } from '../../../utils/px';
import { ThemeStyle } from '../../../typesV2/themeInfo';

export const getItemStyles = (theme: ThemeStyle) => StyleSheet.create({
  // 最外层容器
  container: {
    backgroundColor: theme.common.bg_color,
    marginHorizontal: px(16),
    marginBottom: px(24),
    borderRadius: px(12),
  },
  
  // 商品图片（顶部）
  productImage: {
    width: '100%',
    aspectRatio: 16 / 9,
    backgroundColor: '#f5f5f5',
  },
  
  // 主要内容容器（包含商品信息和按钮）
  contentContainer: {
    flex: 1,
    padding: px(16),
    paddingBottom: px(20),
  },
  
  // 商品信息区域
  productInfo: {
    flex: 1,
  },
  
  // 商品名称
  productName: {
    fontSize: px(20),
    fontWeight: '500',
    color: theme.common.title_color,
    marginBottom: px(12),
  },
  
  // 信息行容器
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: px(8),
  },
  infoLabel: {
    fontSize: px(12),
    color: theme.common.sub_title_color,
    minWidth: px(70),
  },
  infoValue: {
    fontSize: px(12),
    color: theme.common.title_color,
    fontFamily: 'XmlyNumber',
  },
  
  // 兑换码/运单号容器
  codeContainer: {
    height: px(21),
    paddingHorizontal: px(6),
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F7FA',
    borderRadius: px(2),
  },
  copyButton: {
    marginLeft: px(6),
  },
  copyButtonImage: {
    width: px(12),
    height: px(12),
  },
  
  // 分割线
  divider: {
    height: 1,
    backgroundColor: '#CCCCCC33',
  },
  
  // 状态行
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: px(17),
  },
  statusIconImage: {
    width: px(16),
    height: px(16),
    marginRight: px(6),
  },
  statusText: {
    fontSize: px(12),
  },
  statusDetail: {
    fontSize: px(12),
    color: theme.common.sub_title_color,
  },
  
  // 底部按钮
  actionButton: {
    backgroundColor: '#FF4444',
    borderRadius: px(8),
    paddingVertical: px(12),
    paddingHorizontal: px(16),
    marginTop: px(17),
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  buttonIcon: {
    width: px(16),
    height: px(16),
    marginRight: px(6),
  },
  actionButtonText: {
    fontSize: px(14),
    fontWeight: '600',
    color: '#FFFFFF',
  },
});
