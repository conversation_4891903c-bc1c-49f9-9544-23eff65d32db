import React, { useCallback, useContext, useRef } from 'react'
import { ScrollView } from 'react-native'
import isEqual from 'lodash.isequal'
import ListContent from './ListContent'
import {
  CommodityDisplayProductItem,
  CommodityDisplayProducts,
} from '../../../typesV2/commodity'
import StaticListFooter from '../../../componentsV2/common/StaticListFooter'
import { InViewPort } from '@xmly/rn-components'
import {
  ScrollAnalyticWapper,
  ScrollEventSender,
} from '@xmly/react-native-page-analytics'
import CommodityCoverLazyControl from '../../../componentsV2/Categories/CommodityCoverLazyControl'
import { FetchStatus } from '../../../typesV2/common'
import EmptyView from './EmptyView'
import ErrorView from '../../../componentsV2/common/ErrorView'
import xmlog from '../../../utilsV2/xmlog'
import { useNavigation } from '@react-navigation/native'
import { safetyToString } from '@xmly/rn-utils'
import { NativeInfoContext } from '../../../contextV2/nativeInfoContext'

type Props = {
  commodities: CommodityDisplayProducts
  onLoadMore: () => void
  hasMore: boolean
  fetchStatus: FetchStatus
  onReload: () => void
  currentTabId: number
}
const commodityListId = 'commodityListId'
const inViewPortGroupName = 'commodityList'

const List: React.FC<Props> = ({
  commodities,
  onLoadMore,
  hasMore,
  fetchStatus,
  onReload,
  currentTabId,
}) => {
  const nativeInfo = useContext(NativeInfoContext)
  const mIsVip = nativeInfo?.initData?.account?.isVip
  const listFooterInViewChecker = useRef(
    InViewPort.getCheckBatcher(inViewPortGroupName)
  )

  const handleScroll = useCallback(() => {
    // 滚动时发送事件通知，会触发新出现的组件的曝光事件
    ScrollEventSender.send(commodityListId, 'scroll')
    CommodityCoverLazyControl.checker.schedule()
    listFooterInViewChecker.current.schedule()
  }, [])

  const handleCommodityClickEventReport = useCallback(
    (commodity: CommodityDisplayProductItem) => {
      // 任务中心_会员权益-独立商品  点击事件
      xmlog.click(46408, undefined, {
        currPage: 'task_vip',
        productId: safetyToString(commodity.id),
        isVIP: mIsVip ? 'true' : 'false',
        tabId: safetyToString(currentTabId), // 组件的ID（一级tab）
      })
    },
    [mIsVip, currentTabId]
  )

  const handleCommodityShowEventReport = useCallback(
    (commodity: CommodityDisplayProductItem) => {
      console.log()
      // 任务中心_会员权益-独立商品  控件曝光
      xmlog.event(46409, 'slipPage', {
        currPage: 'task_vip',
        productId: safetyToString(commodity.id),
        isVIP: mIsVip ? 'true' : 'false',
        tabId: safetyToString(currentTabId), // 组件的ID（一级tab）
      })
    },
    [mIsVip, currentTabId]
  )

  console.log('[handleCommodityShowEventReport] 未获取到有效IP')
  if (commodities.length === 0) {
    if (fetchStatus === FetchStatus.success) {
      return <EmptyView />
    }

    if (fetchStatus === FetchStatus.fail) {
      return (
        <ErrorView withoutHeader buttonLabel='请重试' onReload={onReload} />
      )
    }
  }

  return (
    <ScrollAnalyticWapper
      id={commodityListId}
      viewStyle={{ flex: 1 }}
      useNavigation={useNavigation}
    >
      <ScrollView
        contentContainerStyle={{ paddingHorizontal: 12 }}
        style={{ flex: 1 }}
        onScroll={handleScroll}
        showsVerticalScrollIndicator={false}
      >
        <ListContent
          commodities={commodities}
          onClickEventReport={handleCommodityClickEventReport}
          onItemShowEventReport={handleCommodityShowEventReport}
        />
        <StaticListFooter
          hasMore={hasMore}
          inViewPortGroupName={inViewPortGroupName}
          onShow={onLoadMore}
        />
      </ScrollView>
    </ScrollAnalyticWapper>
  )
}

export default React.memo(List, isEqual)
