# 大转盘需求文档完整性补充

## 📋 文档评估说明

基于对以下文档的分析：
- 【福利中心】大转盘玩法prd&规则.md
- src/components/CoinCenter/SpiningWheel/ReadMe.md  
- docs/大转盘玩法PRD需求完成度分析.md

发现原始PRD文档存在一些业务逻辑缺失和需要明确的细节，本文档进行补充说明。

## 🔍 业务逻辑缺失补充

### 1. 抽奖次数管理机制

**缺失问题**：PRD未明确说明每日抽奖次数限制

**补充建议**：
- 每日免费抽奖次数：1次
- 付费抽奖次数上限：建议50次/天（防止异常刷量）
- 通过广告获得额外抽奖次数：建议每日最多5次
- 次数重置时间：每日0点重置

### 2. 观看广告获得抽奖机会

**缺失问题**：PRD只提到"看广告可以更新一次付费奖池"，未提及观看广告获得额外抽奖机会

**补充建议**：
- 观看广告获得抽奖机会：每次观看完整广告可获得1次抽奖机会
- 每日限制：最多通过广告获得5次抽奖机会
- 冷却时间：连续观看广告需间隔30秒
- 广告类型：激励视频广告（15-30秒）

### 3. 金币扣除和回滚机制

**缺失问题**：PRD未详细说明付费抽奖的金币扣除流程

**补充建议**：
```
付费抽奖流程：
1. 用户点击抽奖 → 检查金币余额
2. 余额充足 → 显示确认弹窗（"确认消耗10000金币抽奖？"）
3. 用户确认 → 预扣金币（锁定状态）
4. 执行抽奖 → 成功则正式扣除，失败则回滚
5. 异常处理：网络异常、服务端错误等情况下金币自动回滚
```

### 4. 防作弊机制

**缺失问题**：PRD未提及防作弊措施

**补充建议**：
- IP限制：同一IP每日抽奖次数限制100次
- 设备限制：同一设备每日抽奖次数限制50次
- 账号限制：新注册账号24小时内只能参与免费抽奖
- 异常监控：连续中大奖触发人工审核

### 5. 数据统计需求

**缺失问题**：PRD未明确数据统计需求

**补充建议**：
需要统计的关键指标：
- DAU参与率
- 免费/付费抽奖转化率
- 广告观看率和完成率
- 奖品发放成本
- 用户留存率提升
- 金币消耗和回收比

## 📊 业务规则明确化

### 1. 奖池切换规则

**当前描述**：每日首抽结束后，展示付费奖池

**明确规则**：
- 免费奖池 → 付费奖池：首次免费抽奖完成后立即切换
- 付费奖池 → 免费奖池：每日0点自动切换
- 跨天处理：用户在23:59开始抽奖，0:00完成，按开始时间计算

### 2. iPhone库存管理

**当前描述**：每月限量5个，抽完后仍可展示但不可抽中

**明确规则**：
- 库存扣减：实时扣减，避免超发
- 库存显示：剩余0个时，界面显示"本月已抽完"
- 库存补充：每月1日0点补充5个
- 并发控制：使用分布式锁防止超发

### 3. 实物奖品兑换流程

**当前描述**：7个工作日内兑换，次月月底前发放

**明确流程**：
```
1. 中奖通知（站内信+推送）
2. 填写收货信息（姓名、电话、地址、身份证号）
3. 实名认证校验
4. 信息审核（1-3个工作日）
5. 审核通过，等待发货
6. 物流跟踪
7. 签收确认
```

## 🔧 技术实现细节补充

### 1. 接口设计补充

**缺失的接口**：
```typescript
// 1. 获得抽奖机会接口
POST /api/v2/spinning/chance/gain
{
  type: 'ad' | 'purchase',  // 广告或购买
  adId?: string,           // 广告ID
  coins?: number           // 购买消耗金币
}

// 2. 扣除金币接口
POST /api/v2/spinning/coins/deduct
{
  amount: number,          // 扣除金额
  orderId: string,         // 订单ID
  type: 'spinning'         // 类型
}

// 3. 查询抽奖次数接口
GET /api/v2/spinning/chance/query
Response: {
  freeChances: number,     // 免费次数
  adChances: number,       // 广告获得次数
  purchasedChances: number // 购买次数
}
```

### 2. 异常处理规范

**网络异常**：
- 3次重试机制，间隔1秒、2秒、4秒
- 重试失败后提示用户稍后再试

**服务端异常**：
- 5xx错误：金币自动回滚，提示系统维护
- 4xx错误：根据错误码显示具体提示

**客户端异常**：
- 动画中断：保存抽奖结果，刷新后显示
- 应用崩溃：服务端保存30分钟抽奖结果

### 3. 性能优化建议

**预加载**：
- 进入福利中心即预加载转盘资源
- 预请求抽奖配置信息

**缓存策略**：
- 奖品配置缓存5分钟
- 中奖跑马灯缓存1分钟
- 用户抽奖次数实时查询

**动画优化**：
- 使用GPU加速
- 离屏渲染优化
- 60fps流畅度保证

## 📱 用户体验补充

### 1. 新手引导

**首次使用**：
- 高亮提示"每日首次免费"
- 手势引导点击抽奖按钮
- 弹窗说明抽奖规则

### 2. 中奖反馈

**视觉反馈**：
- 中奖金额飘字动画
- 大奖烟花特效
- 震动反馈（可选）

**音效反馈**：
- 转盘转动音效
- 中奖音效（分级别）
- 背景音乐（可关闭）

### 3. 异常提示优化

**金币不足**：
- 提示具体差额："还差3000金币"
- 提供快捷获取金币入口

**网络异常**：
- 断网提示："网络不给力，请检查网络"
- 提供重试按钮

## 🎯 产品目标补充

### 成功指标定义

**核心指标**：
- 福利中心留存率提升10%
- 金币消耗量增加20%
- 广告收入增加15%

**过程指标**：
- 免费抽奖参与率 > 80%
- 付费抽奖转化率 > 30%
- 人均抽奖次数 > 3次/天

### 风险控制

**成本风险**：
- 设置每日成本上限
- 大奖中奖率动态调整
- 异常用户自动限制

**合规风险**：
- 明确标注中奖概率
- 保存抽奖记录1年
- 实物奖品需合规报备

## 📝 总结

原始PRD文档在业务场景、功能描述方面较为完整，但在以下方面需要补充：

1. **技术实现细节**：接口设计、异常处理、性能优化
2. **业务规则细化**：次数限制、防作弊、库存管理  
3. **用户体验设计**：新手引导、反馈机制、异常提示
4. **数据和监控**：统计指标、风险控制、成本监控

建议产品、技术、设计团队基于此补充文档进行详细评审，确保各方理解一致。