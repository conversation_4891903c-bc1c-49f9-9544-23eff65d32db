# React Native 开发环境问题解决方案

## 🚨 问题描述

执行 `yarn start` 后扫二维码报错：
```
Error: error:0308010C:digital envelope routines::unsupported
```

这是 Node.js 17+ 版本的 OpenSSL 兼容性问题。

## 🔧 解决方案

### 方案1: 使用修改后的启动命令（推荐）

项目的 `package.json` 已经更新，直接使用：

```bash
yarn start
```

现在的启动命令已经包含了 `--openssl-legacy-provider` 参数。

### 方案2: 使用便捷启动脚本

```bash
./start-dev.sh
```

### 方案3: 使用项目指定的 Node.js 版本（最佳）

项目配置了 `.nvmrc` 文件，指定使用 Node.js v16.13.0：

```bash
# 如果安装了 nvm
nvm use

# 然后启动项目
yarn start
```

### 方案4: 手动设置环境变量

```bash
export NODE_OPTIONS="--openssl-legacy-provider"
yarn start
```

### 方案5: 使用环境配置脚本

```bash
./dev-setup.sh
```

## 📋 推荐的开发流程

1. **首次设置**：
   ```bash
   # 运行环境配置脚本
   ./dev-setup.sh
   
   # 或者手动切换 Node.js 版本
   nvm use
   ```

2. **日常开发**：
   ```bash
   # 直接启动（已配置 legacy OpenSSL）
   yarn start
   
   # 或使用便捷脚本
   ./start-dev.sh
   ```

3. **扫描二维码调试**：
   - 启动成功后会显示二维码
   - 使用手机扫描进行调试

## 🔍 问题原因

- **Node.js 17+** 使用了 OpenSSL 3.0
- **旧的加密算法** 不再被默认支持
- **Metro bundler** 使用了这些旧算法
- **解决方案** 是启用 legacy OpenSSL 提供程序

## 💡 长期建议

1. **使用 nvm 管理 Node.js 版本**
2. **项目团队统一使用 Node.js v16.x**
3. **等待 Metro bundler 更新支持新的 OpenSSL**

## 🛠️ 故障排除

### 如果仍然报错：

1. **清理缓存**：
   ```bash
   npx react-native start --reset-cache
   ```

2. **重新安装依赖**：
   ```bash
   rm -rf node_modules
   yarn install
   ```

3. **检查 Node.js 版本**：
   ```bash
   node --version
   ```

4. **使用 Node.js v16**：
   ```bash
   nvm install 16.20.0
   nvm use 16.20.0
   ```

### 常见错误信息：

- `ERR_OSSL_EVP_UNSUPPORTED`
- `digital envelope routines::unsupported`
- `Cannot read properties of undefined (reading 'transformFile')`

这些都是同一个 OpenSSL 兼容性问题的不同表现。

## 📞 技术支持

如果以上方案都无法解决问题，请：

1. 检查 Node.js 版本：`node --version`
2. 检查 npm 版本：`npm --version`
3. 检查 yarn 版本：`yarn --version`
4. 提供完整的错误日志

---

**最后更新**: 2025-01-17  
**适用版本**: Node.js 17+, React Native 0.x
