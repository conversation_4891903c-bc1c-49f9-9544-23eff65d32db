# CLAUDE.md

 请用中文回复

此文件为Claude Code (claude.ai/code) 在使用此代码库时提供指导。

## 项目概述

这是一个React Native积分中心应用，管理用户奖励、任务和福利功能。项目采用双版本方式，许多模块都有v1和v2实现，表明正在进行迁移。

## 基本开发命令

### 开发环境
```bash
# 启动开发服务器
yarn start

# 启用调试模式启动
yarn start:debug

# 运行代码检查（提交前必须运行）
yarn lint
```

### 构建
```bash
# Android构建
yarn pack:android:test  # 测试环境
yarn pack:android:prod  # 生产环境

# iOS构建
yarn pack:ios:test      # 测试环境
yarn pack:ios:prod      # 生产环境
```

## 架构概述

### 状态管理
- **Redux with Rematch** (`@rematch/core`) - 主要状态管理，位于 `src/models/` 和 `src/modelsV2/`
- **Jotai** - 原子化状态管理，位于 `src/atom/`
- **React Context** - 主题和用户信息，位于 `src/contextV2/`

### 目录结构
```
src/
├── componentsV2/     # 当前UI组件（优先使用，而非components/）
├── pages/            # 页面组件
├── router/           # React Navigation v5配置
├── servicesV2/       # API服务（优先使用，而非services/）
├── modelsV2/         # Redux模型（优先使用，而非models/）
├── utilsV2/          # 工具函数（优先使用，而非utils/）
├── hooksV2/          # 自定义钩子（优先使用，而非hooks/）
├── atom/             # Jotai原子
├── contextV2/        # React上下文
├── typesV2/          # TypeScript类型
├── constantsV2/      # 常量
├── storageV2/        # AsyncStorage工具
└── themeV2/          # 主题配置
```

### 核心功能
1. **任务系统** - 每日任务、换量任务、视频任务、第三方任务和有价值任务
2. **奖励系统** - 积分、金币、月票、礼包
3. **用户功能** - 签到、钱包、通知、分享

### 导航
使用React Navigation v5的标签式结构。主导航配置在 `src/router/`。

### API服务
- 服务分为 `services/` (v1) 和 `servicesV2/` (v2)
- 优先使用v2服务
- API端点遵循 `/api/v1/` 或 `/api/v2/` 模式

### 样式
- **Styled Components v3.4.10** 用于组件样式
- 支持明/暗主题，位于 `src/themeV2/`
- Prettier配置使用长行宽（180字符）

### TypeScript配置
- 路径别名：`@/*` 映射到 `./src/*`
- 启用严格模式
- 目标：ES5与ESNext模块

## 重要开发注意事项

1. **双版本管理**：修改代码时，检查是否存在v1和v2版本。始终优先使用v2。

2. **内部包**：大量使用 `@xmly/` 作用域包，用于UI组件、SDK和工具。

3. **换量任务功能**：在 `mock_data/` 目录中有换量任务功能的详细文档。

4. **无测试**：项目配置了Jest但没有测试文件。测试不是开发工作流的一部分。

5. **代码检查**：提交前始终运行 `yarn lint`。ESLint配置了宽松规则。

6. **模拟数据**：可使用Charles代理进行API模拟。参见 `mock_data/QUICK_SETUP.md`。

## 常见任务

### 添加新页面
1. 在 `src/pages/` 中创建组件
2. 在 `src/router/` 中添加路由
3. 在 `src/typesV2/` 中更新导航类型

### 添加新API服务
1. 在 `src/servicesV2/` 中创建服务
2. 在 `src/typesV2/` 中添加类型
3. 在 `src/modelsV2/` 中更新相关Redux模型

### 状态管理
1. 全局状态：使用 `src/modelsV2/` 中的Redux模型
2. 组件状态：使用 `src/atom/` 中的Jotai原子
3. 主题/用户信息：使用 `src/contextV2/` 中的上下文

### 调试
- 使用 `yarn start:debug` 进行Node.js调试
- 可用React Native Debugger集成
- 使用Charles代理进行API调试（参见mock_data文档）
